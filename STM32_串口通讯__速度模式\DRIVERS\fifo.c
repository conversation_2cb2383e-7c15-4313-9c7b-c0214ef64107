#include "fifo.h"

/**********************************************************
***	Emm_V5.0�����ջ���������
***	��д���ߣ�ZHANGDATOU
***	����֧�֣��Ŵ�ͷ�ջ��ŷ�
***	�Ա����̣�https://zhangdatou.taobao.com
***	CSDN���ͣ�http s://blog.csdn.net/zhangdatou666
***	qq����Ⱥ��262438510
**********************************************************/

__IO FIFO_t rxFIFO1 = {0}; // USART1 FIFO
__IO FIFO_t rxFIFO2 = {0}; // USART2 FIFO

/**
	* @brief   ��ʼ������
	* @param   fifo - FIFO����ָ��
	* @retval  ��
	*/
void fifo_initQueue(FIFO_t *fifo)
{
	fifo->ptrRead  = 0;
	fifo->ptrWrite = 0;
}

/**
	* @brief   ���
	* @param   fifo - FIFO����ָ��, data - ����
	* @retval  ��
	*/
void fifo_enQueue(FIFO_t *fifo, uint16_t data)
{
	fifo->buffer[fifo->ptrWrite] = data;

	++fifo->ptrWrite;

	if(fifo->ptrWrite >= FIFO_SIZE)
	{
		fifo->ptrWrite = 0;
	}
}

/**
	* @brief   ����
	* @param   fifo - FIFO����ָ��
	* @retval  ������
	*/
uint16_t fifo_deQueue(FIFO_t *fifo)
{
	uint16_t element = 0;

	element = fifo->buffer[fifo->ptrRead];

	++fifo->ptrRead;

	if(fifo->ptrRead >= FIFO_SIZE)
	{
		fifo->ptrRead = 0;
	}

	return element;
}

/**
	* @brief   �жϿն���
	* @param   fifo - FIFO����ָ��
	* @retval  true-�գ�false-�ǿ�
	*/
bool fifo_isEmpty(FIFO_t *fifo)
{
	if(fifo->ptrRead == fifo->ptrWrite)
	{
		return true;
	}

	return false;
}

/**
	* @brief   ������г���
	* @param   fifo - FIFO����ָ��
	* @retval  ���г���
	*/
uint16_t fifo_queueLength(FIFO_t *fifo)
{
	if(fifo->ptrRead <= fifo->ptrWrite)
	{
		return (fifo->ptrWrite - fifo->ptrRead);
	}
	else
	{
		return (FIFO_SIZE - fifo->ptrRead + fifo->ptrWrite);
	}
}
