# 位置模式控制详解 - 180°每秒旋转

## 🎯 需求分析

### 目标
- **电机1**：顺时针旋转180°每秒
- **电机2**：逆时针旋转180°每秒

### 关键参数计算
```
驱动器设置：3200脉冲/圈（1.8°步进角，16细分）
180° = 180° × (3200脉冲/360°) = 1600脉冲
180°/秒 = 1600脉冲/秒 × 60秒/分钟 ÷ (3200脉冲/圈) = 1600RPM
```

## 📋 位置控制函数详解

### 核心函数
```c
// 电机1（USART1）
Emm_V5_Pos_Control(uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF);

// 电机2（USART2）  
Emm_V5_Pos_Control_USART(uint8_t usart_num, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF);
```

### 参数详细说明

| 参数 | 类型 | 说明 | 电机1的值 | 电机2的值 |
|------|------|------|-----------|-----------|
| usart_num | uint8_t | 串口号 | - | 2 |
| addr | uint8_t | 电机地址 | 1 | 1 |
| dir | uint8_t | 旋转方向 | 0（顺时针） | 1（逆时针） |
| vel | uint16_t | 速度(RPM) | 1600 | 1600 |
| acc | uint8_t | 加速度 | 15 | 15 |
| clk | uint32_t | 脉冲数 | 1600 | 1600 |
| raF | bool | 位置模式 | false（相对位置） | false（相对位置） |
| snF | bool | 同步标志 | false（立即执行） | false（立即执行） |

## 🔧 基本控制逻辑

### 1. 系统初始化
```c
board_init();        // 初始化硬件（USART1, USART2, GPIO等）
delay_ms(2000);      // 等待驱动器上电初始化
```

### 2. 电机使能
```c
Emm_V5_En_Control(1, true, false);           // 使能电机1
Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机2
delay_ms(100);                               // 等待使能完成
```

### 3. 位置控制
```c
// 电机1：顺时针180°，1600RPM，加速度15，1600脉冲
Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);

// 电机2：逆时针180°，1600RPM，加速度15，1600脉冲
Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);
```

### 4. 等待运动完成
```c
delay_ms(1200);  // 等待180°运动完成（理论1秒+加减速时间）
```

## ⚙️ 关键概念解释

### 位置模式 vs 速度模式

| 特性 | 位置模式 | 速度模式 |
|------|----------|----------|
| 控制目标 | 精确角度 | 连续旋转 |
| 停止方式 | 自动停止 | 手动停止 |
| 应用场景 | 定位控制 | 连续运行 |
| 参数重点 | 脉冲数 | 速度RPM |

### 相对位置 vs 绝对位置

```c
// 相对位置（raF = false）：从当前位置开始计算
Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);  // 从当前位置顺时针转180°

// 绝对位置（raF = true）：移动到指定的绝对位置
Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, true, false);   // 移动到绝对位置1600脉冲处
```

### 同步控制

```c
// 设置同步运动（snF = true）
Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, true);
Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, true);

// 触发同步运动
Emm_V5_Synchronous_motion(1);  // 两个电机同时开始运动
```

## 🕐 时间计算

### 理论运动时间
```
运动时间 = 脉冲数 ÷ (速度RPM × 脉冲数每圈 ÷ 60秒)
180°运动时间 = 1600脉冲 ÷ (1600RPM × 3200脉冲每圈 ÷ 60秒) = 1秒
```

### 实际等待时间
```c
delay_ms(1200);  // 理论1秒 + 加减速时间200ms
```

## 💡 实际应用示例

### 示例1：基本180°旋转
```c
void basic_180_rotation(void)
{
    // 初始化
    board_init();
    delay_ms(2000);
    
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 180°旋转
    Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);        // 电机1顺时针
    Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false); // 电机2逆时针
    
    delay_ms(1200);  // 等待完成
}
```

### 示例2：连续180°旋转
```c
void continuous_180_rotation(void)
{
    // 初始化和使能...
    
    // 连续旋转10次
    for(int i = 0; i < 10; i++)
    {
        Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);
        delay_ms(1200);  // 等待每次旋转完成
        delay_ms(500);   // 间隔0.5秒
    }
}
```

### 示例3：往返运动
```c
void reciprocating_motion(void)
{
    // 初始化和使能...
    
    for(int cycle = 0; cycle < 5; cycle++)
    {
        // 正向180°
        Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);
        delay_ms(1200);
        
        delay_ms(1000);  // 停顿1秒
        
        // 反向180°
        Emm_V5_Pos_Control(1, 1, 1600, 15, 1600, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 0, 1600, 15, 1600, false, false);
        delay_ms(1200);
        
        delay_ms(1000);  // 停顿1秒
    }
}
```

## 🔍 调试和优化

### 参数调整建议

1. **速度调整**：
   - 慢速：800RPM（180°需2秒）
   - 标准：1600RPM（180°需1秒）
   - 快速：3200RPM（180°需0.5秒）

2. **加速度调整**：
   - 平滑启动：5-10
   - 标准启动：10-20
   - 快速启动：20-30

3. **等待时间调整**：
   ```c
   // 根据实际速度调整等待时间
   uint16_t wait_time = (uint16_t)(1600.0 / speed * 1000) + 200;  // 理论时间+200ms
   delay_ms(wait_time);
   ```

### 常见问题解决

1. **运动不准确**：
   - 检查脉冲数计算
   - 确认驱动器细分设置
   - 使用位置复位功能

2. **运动不同步**：
   - 使用同步控制功能
   - 确保两个电机参数一致

3. **启动抖动**：
   - 调整加速度参数
   - 检查电机使能状态

## 📊 性能对比

| 控制方式 | 精度 | 复杂度 | 适用场景 |
|----------|------|--------|----------|
| 位置模式 | 高 | 中 | 定位、往返运动 |
| 速度模式 | 中 | 低 | 连续旋转 |
| 同步控制 | 高 | 高 | 多轴协调运动 |

通过以上详细说明，您可以完全掌握位置模式控制的原理和实现方法，实现精确的180°每秒旋转控制。
