/**********************************************************
***	双电机控制示例代码
***	作者：基于ZHANGDATOU原始代码扩展
***	功能：演示如何同时控制两个Emm_V5.0步进电机
***	硬件：STM32F103 + 两个Emm_V5.0驱动器
**********************************************************/

#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**
 * @brief 双电机基本控制示例
 */
void dual_motor_basic_example(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000); // 等待驱动器初始化
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);           // 使能电机1 (USART1)
    Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机2 (USART2)
    delay_ms(100);
    
    // 电机1：顺时针1000RPM，加速度10
    Emm_V5_Vel_Control(1, 0, 1000, 10, false);
    
    // 电机2：逆时针800RPM，加速度15  
    Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
    
    delay_ms(5000); // 运行5秒
    
    // 停止两个电机
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
}

/**
 * @brief 双电机同步控制示例
 */
void dual_motor_sync_example(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置同步运动 - 两个电机同时启动
    Emm_V5_Vel_Control(1, 0, 1000, 10, true);        // 电机1，同步标志=true
    Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, true); // 电机2，同步标志=true
    
    // 触发同步运动
    Emm_V5_Synchronous_motion(1);                     // 可以用任一电机地址触发
    
    delay_ms(3000);
    
    // 同步停止
    Emm_V5_Stop_Now(1, true);
    Emm_V5_Stop_Now_USART(2, 1, true);
    Emm_V5_Synchronous_motion(1); // 触发同步停止
}

/**
 * @brief 双电机位置控制示例
 */
void dual_motor_position_example(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 位置控制：电机1转动3200脉冲（1圈），电机2转动6400脉冲（2圈）
    Emm_V5_Pos_Control(1, 0, 500, 10, 3200, false, false);        // 电机1
    Emm_V5_Pos_Control_USART(2, 1, 1, 500, 10, 6400, false, false); // 电机2
    
    // 等待运动完成（根据实际情况调整时间）
    delay_ms(10000);
    
    // 复位位置
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
}

/**
 * @brief 双电机交替运行示例
 */
void dual_motor_alternate_example(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    for(int i = 0; i < 5; i++)
    {
        // 电机1运行
        Emm_V5_Vel_Control(1, 0, 1000, 10, false);
        delay_ms(2000);
        Emm_V5_Stop_Now(1, false);
        delay_ms(500);
        
        // 电机2运行
        Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
        delay_ms(2000);
        Emm_V5_Stop_Now_USART(2, 1, false);
        delay_ms(500);
    }
}

/**
 * @brief 双电机状态监控示例
 */
void dual_motor_monitor_example(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 启动电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    Emm_V5_Vel_Control(1, 0, 500, 10, false);
    Emm_V5_Vel_Control_USART(2, 1, 1, 500, 10, false);
    
    // 监控循环
    for(int i = 0; i < 10; i++)
    {
        // 读取电机1状态
        Emm_V5_Read_Sys_Params(1, S_VEL);    // 读取速度
        while(rxFrameFlag1 == false); rxFrameFlag1 = false; // 等待响应
        
        // 读取电机2状态  
        Emm_V5_Read_Sys_Params_USART(2, 1, S_VEL); // 读取速度
        while(rxFrameFlag2 == false); rxFrameFlag2 = false; // 等待响应
        
        delay_ms(1000);
    }
    
    // 停止电机
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
}

/**
 * @brief 主函数示例
 */
int main_example(void)
{
    // 选择要运行的示例
    // dual_motor_basic_example();      // 基本控制
    // dual_motor_sync_example();       // 同步控制
    // dual_motor_position_example();   // 位置控制
    // dual_motor_alternate_example();  // 交替运行
    dual_motor_monitor_example();       // 状态监控
    
    while(1)
    {
        // 主循环
        delay_ms(100);
    }
}

/**
 * @brief 错误处理和故障诊断
 */
void motor_error_handling(void)
{
    // 检查通信状态
    if(rxFrameFlag1 == false && rxFrameFlag2 == false)
    {
        // 可能的通信问题
        // 1. 检查硬件连接
        // 2. 检查波特率设置
        // 3. 检查电源供应
    }
    
    // 紧急停止所有电机
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
    
    // 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}
