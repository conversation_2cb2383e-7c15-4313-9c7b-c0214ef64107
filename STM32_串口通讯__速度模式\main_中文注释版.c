#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库 - 双电机控制示例
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：同时控制两个步进电机
**********************************************************/

/**
	*	@brief		主函数 - 双电机控制演示
	*	@param		无
	*	@retval		无
	*/
int main(void)
{
/**********************************************************
***	初始化系统硬件
**********************************************************/
	board_init();

/**********************************************************
***	上电延时2秒等待Emm_V5.0驱动器初始化完成
**********************************************************/	
	delay_ms(2000);

/**********************************************************
***	双电机速度模式控制演示
***	电机1(USART1): 顺时针方向，速度1000RPM，加速度10
***	电机2(USART2): 逆时针方向，速度800RPM，加速度15
**********************************************************/	
  // 电机1 - 使用USART1控制，地址1
  Emm_V5_Vel_Control(1, 0, 1000, 10, 0);
  
  // 电机2 - 使用USART2控制，地址1
  Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, 0);
	
/**********************************************************
***	等待串口接收数据缓存到数组rxCmd上，长度为rxCount
***	注意：演示程序等待USART1的响应
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	主循环 - 双电机演示程序
***	每5秒切换一次电机转向和速度
**********************************************************/	
	while(1)
	{
		// 每5秒切换一次电机转向
		delay_ms(5000);
		
		// 停止两个电机
		Emm_V5_Stop_Now(1, false);           // 停止电机1
		Emm_V5_Stop_Now_USART(2, 1, false);  // 停止电机2
		delay_ms(1000);
		
		// 切换转向
		Emm_V5_Vel_Control(1, 1, 800, 10, 0);        // 电机1逆时针800RPM
		Emm_V5_Vel_Control_USART(2, 1, 0, 1200, 15, 0); // 电机2顺时针1200RPM
		delay_ms(5000);
		
		// 停止两个电机
		Emm_V5_Stop_Now(1, false);
		Emm_V5_Stop_Now_USART(2, 1, false);
		delay_ms(1000);
		
		// 恢复初始状态
		Emm_V5_Vel_Control(1, 0, 1000, 10, 0);       // 电机1顺时针1000RPM
		Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, 0);  // 电机2逆时针800RPM
	}
}
