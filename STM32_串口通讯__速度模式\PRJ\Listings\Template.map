Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.main) refers to board.o(i.board_init) for board_init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_En_Control_USART) for Emm_V5_En_Control_USART
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Vel_Control_USART) for Emm_V5_Vel_Control_USART
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Stop_Now_USART) for Emm_V5_Stop_Now_USART
    main.o(i.main) refers to usart.o(.data) for rxFrameFlag
    emm_v5.o(i.Emm_V5_En_Control) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_En_Control_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Interrupt_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Modify_Params_USART) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Set_O_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Pos_Control) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Pos_Control_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Read_Sys_Params_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_SendCmd_USART) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_SendCmd_USART) refers to usart.o(i.usart2_SendCmd) for usart2_SendCmd
    emm_v5.o(i.Emm_V5_Stop_Now) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Stop_Now_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Synchronous_motion_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Vel_Control_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_enQueue) for fifo_enQueue
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_deQueue) for fifo_deQueue
    usart.o(i.USART1_IRQHandler) refers to fifo.o(.bss) for rxFIFO1
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for rxCount1
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for rxCmd1
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_enQueue) for fifo_enQueue
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    usart.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_deQueue) for fifo_deQueue
    usart.o(i.USART2_IRQHandler) refers to fifo.o(.bss) for rxFIFO2
    usart.o(i.USART2_IRQHandler) refers to usart.o(.data) for rxCount2
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for rxCmd2
    usart.o(i.usart1_SendCmd) refers to usart.o(i.usart1_SendByte) for usart1_SendByte
    usart.o(i.usart2_SendCmd) refers to usart.o(i.usart2_SendByte) for usart2_SendByte
    usart.o(i.usart_SendByte) refers to usart.o(i.usart1_SendByte) for usart1_SendByte
    usart.o(i.usart_SendCmd) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    board.o(i.board_init) refers to board.o(i.nvic_init) for nvic_init
    board.o(i.board_init) refers to board.o(i.clock_init) for clock_init
    board.o(i.board_init) refers to fifo.o(i.fifo_initQueue) for fifo_initQueue
    board.o(i.board_init) refers to board.o(i.usart_init) for usart_init
    board.o(i.board_init) refers to fifo.o(.bss) for rxFIFO1
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    board.o(i.clock_init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    board.o(i.usart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    delay.o(i.delay_ms) refers to system_stm32f10x.o(.data) for SystemCoreClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (58 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode_USART), (66 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt_USART), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (170 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params_USART), (176 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O_USART), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return_USART), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Pos_Control), (108 bytes).
    Removing emm_v5.o(i.Emm_V5_Pos_Control_USART), (112 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (262 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params_USART), (270 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro_USART), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero_USART), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion_USART), (50 bytes).
    Removing usart.o(i.usart_SendByte), (12 bytes).
    Removing usart.o(i.usart_SendCmd), (16 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing fifo.o(i.fifo_isEmpty), (22 bytes).
    Removing delay.o(i.delay_cnt), (10 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).

99 unused section(s) (total 4548 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ..\APP\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\APP\stm32f10x_it.c                    0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\BSP\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\BSP\board.c                           0x00000000   Number         0  board.o ABSOLUTE
    ..\BSP\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\DRIVERS\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    ..\DRIVERS\fifo.c                        0x00000000   Number         0  fifo.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section       36  init.o(.text)
    i.BusFault_Handler                       0x0800018c   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000190   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x08000192   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_En_Control_USART                0x080001cc   Section        0  emm_v5.o(i.Emm_V5_En_Control_USART)
    i.Emm_V5_SendCmd_USART                   0x0800020e   Section        0  emm_v5.o(i.Emm_V5_SendCmd_USART)
    Emm_V5_SendCmd_USART                     0x0800020f   Thumb Code    36  emm_v5.o(i.Emm_V5_SendCmd_USART)
    i.Emm_V5_Stop_Now                        0x08000232   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Emm_V5_Stop_Now_USART                  0x08000266   Section        0  emm_v5.o(i.Emm_V5_Stop_Now_USART)
    i.Emm_V5_Vel_Control                     0x0800029e   Section        0  emm_v5.o(i.Emm_V5_Vel_Control)
    i.Emm_V5_Vel_Control_USART               0x080002ec   Section        0  emm_v5.o(i.Emm_V5_Vel_Control_USART)
    i.GPIO_Init                              0x0800033c   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x08000454   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.HardFault_Handler                      0x080004e4   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080004e8   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080004ec   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080004f0   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000560   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08000574   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000578   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000598   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080005b8   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x0800068c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x0800068e   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x0800068f   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000698   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000699   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000778   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800077c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080007dc   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080008b8   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART_ClearITPendingBit                0x0800094c   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x0800096a   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08000982   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080009d6   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08000a20   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.UsageFault_Handler                     0x08000af8   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x08000afc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08000b0a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08000b0c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.board_init                             0x08000b1c   Section        0  board.o(i.board_init)
    i.clock_init                             0x08000b40   Section        0  board.o(i.clock_init)
    i.delay_ms                               0x08000b68   Section        0  delay.o(i.delay_ms)
    i.fifo_deQueue                           0x08000bc8   Section        0  fifo.o(i.fifo_deQueue)
    i.fifo_enQueue                           0x08000bee   Section        0  fifo.o(i.fifo_enQueue)
    i.fifo_initQueue                         0x08000c10   Section        0  fifo.o(i.fifo_initQueue)
    i.fifo_queueLength                       0x08000c1c   Section        0  fifo.o(i.fifo_queueLength)
    i.main                                   0x08000c4c   Section        0  main.o(i.main)
    i.nvic_init                              0x08000d4c   Section        0  board.o(i.nvic_init)
    i.usart1_SendByte                        0x08000d88   Section        0  usart.o(i.usart1_SendByte)
    i.usart1_SendCmd                         0x08000dc4   Section        0  usart.o(i.usart1_SendCmd)
    i.usart2_SendByte                        0x08000df0   Section        0  usart.o(i.usart2_SendByte)
    i.usart2_SendCmd                         0x08000e2c   Section        0  usart.o(i.usart2_SendCmd)
    i.usart_init                             0x08000e58   Section        0  board.o(i.usart_init)
    .data                                    0x20000000   Section        6  usart.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f10x.o(.data)
    .data                                    0x2000001c   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000001c   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000002c   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x20000030   Section      384  usart.o(.bss)
    .bss                                     0x200001b0   Section      516  fifo.o(.bss)
    STACK                                    0x200003b8   Section     2048  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __scatterload                            0x08000169   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000169   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x0800018d   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000191   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x08000193   Thumb Code    58  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_En_Control_USART                  0x080001cd   Thumb Code    66  emm_v5.o(i.Emm_V5_En_Control_USART)
    Emm_V5_Stop_Now                          0x08000233   Thumb Code    52  emm_v5.o(i.Emm_V5_Stop_Now)
    Emm_V5_Stop_Now_USART                    0x08000267   Thumb Code    56  emm_v5.o(i.Emm_V5_Stop_Now_USART)
    Emm_V5_Vel_Control                       0x0800029f   Thumb Code    78  emm_v5.o(i.Emm_V5_Vel_Control)
    Emm_V5_Vel_Control_USART                 0x080002ed   Thumb Code    80  emm_v5.o(i.Emm_V5_Vel_Control_USART)
    GPIO_Init                                0x0800033d   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x08000455   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    HardFault_Handler                        0x080004e5   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080004e9   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080004ed   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x080004f1   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000561   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08000575   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000579   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000599   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080005b9   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x0800068d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000779   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x0800077d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_IRQHandler                        0x080007dd   Thumb Code   186  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080008b9   Thumb Code   126  usart.o(i.USART2_IRQHandler)
    USART_ClearITPendingBit                  0x0800094d   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x0800096b   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08000983   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080009d7   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08000a21   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    UsageFault_Handler                       0x08000af9   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08000afd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08000b0b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08000b0d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    board_init                               0x08000b1d   Thumb Code    28  board.o(i.board_init)
    clock_init                               0x08000b41   Thumb Code    36  board.o(i.clock_init)
    delay_ms                                 0x08000b69   Thumb Code    90  delay.o(i.delay_ms)
    fifo_deQueue                             0x08000bc9   Thumb Code    38  fifo.o(i.fifo_deQueue)
    fifo_enQueue                             0x08000bef   Thumb Code    34  fifo.o(i.fifo_enQueue)
    fifo_initQueue                           0x08000c11   Thumb Code    12  fifo.o(i.fifo_initQueue)
    fifo_queueLength                         0x08000c1d   Thumb Code    46  fifo.o(i.fifo_queueLength)
    main                                     0x08000c4d   Thumb Code   250  main.o(i.main)
    nvic_init                                0x08000d4d   Thumb Code    58  board.o(i.nvic_init)
    usart1_SendByte                          0x08000d89   Thumb Code    56  usart.o(i.usart1_SendByte)
    usart1_SendCmd                           0x08000dc5   Thumb Code    44  usart.o(i.usart1_SendCmd)
    usart2_SendByte                          0x08000df1   Thumb Code    56  usart.o(i.usart2_SendByte)
    usart2_SendCmd                           0x08000e2d   Thumb Code    44  usart.o(i.usart2_SendCmd)
    usart_init                               0x08000e59   Thumb Code   254  board.o(i.usart_init)
    Region$$Table$$Base                      0x08000f64   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08000f84   Number         0  anon$$obj.o(Region$$Table)
    rxFrameFlag1                             0x20000000   Data           1  usart.o(.data)
    rxCount1                                 0x20000001   Data           1  usart.o(.data)
    rxFrameFlag2                             0x20000002   Data           1  usart.o(.data)
    rxCount2                                 0x20000003   Data           1  usart.o(.data)
    rxFrameFlag                              0x20000004   Data           1  usart.o(.data)
    rxCount                                  0x20000005   Data           1  usart.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f10x.o(.data)
    rxCmd1                                   0x20000030   Data         128  usart.o(.bss)
    rxCmd2                                   0x200000b0   Data         128  usart.o(.bss)
    rxCmd                                    0x20000130   Data         128  usart.o(.bss)
    rxFIFO1                                  0x200001b0   Data         258  fifo.o(.bss)
    rxFIFO2                                  0x200002b2   Data         258  fifo.o(.bss)
    __initial_sp                             0x20000bb8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00000fb4, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00000f84, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          398    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         1027  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         1032    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         1035    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1037    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1039    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         1040    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1042    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1044    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         1033    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          399    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000024   Code   RO         1046    .text               mc_w.l(init.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO           64    i.BusFault_Handler  stm32f10x_it.o
    0x08000190   0x08000190   0x00000002   Code   RO           65    i.DebugMon_Handler  stm32f10x_it.o
    0x08000192   0x08000192   0x0000003a   Code   RO          127    i.Emm_V5_En_Control  emm_v5.o
    0x080001cc   0x080001cc   0x00000042   Code   RO          128    i.Emm_V5_En_Control_USART  emm_v5.o
    0x0800020e   0x0800020e   0x00000024   Code   RO          147    i.Emm_V5_SendCmd_USART  emm_v5.o
    0x08000232   0x08000232   0x00000034   Code   RO          148    i.Emm_V5_Stop_Now   emm_v5.o
    0x08000266   0x08000266   0x00000038   Code   RO          149    i.Emm_V5_Stop_Now_USART  emm_v5.o
    0x0800029e   0x0800029e   0x0000004e   Code   RO          152    i.Emm_V5_Vel_Control  emm_v5.o
    0x080002ec   0x080002ec   0x00000050   Code   RO          153    i.Emm_V5_Vel_Control_USART  emm_v5.o
    0x0800033c   0x0800033c   0x00000116   Code   RO          503    i.GPIO_Init         stm32f10x_gpio.o
    0x08000452   0x08000452   0x00000002   PAD
    0x08000454   0x08000454   0x00000090   Code   RO          505    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x080004e4   0x080004e4   0x00000004   Code   RO           66    i.HardFault_Handler  stm32f10x_it.o
    0x080004e8   0x080004e8   0x00000004   Code   RO           67    i.MemManage_Handler  stm32f10x_it.o
    0x080004ec   0x080004ec   0x00000002   Code   RO           68    i.NMI_Handler       stm32f10x_it.o
    0x080004ee   0x080004ee   0x00000002   PAD
    0x080004f0   0x080004f0   0x00000070   Code   RO          991    i.NVIC_Init         misc.o
    0x08000560   0x08000560   0x00000014   Code   RO          992    i.NVIC_PriorityGroupConfig  misc.o
    0x08000574   0x08000574   0x00000002   Code   RO           69    i.PendSV_Handler    stm32f10x_it.o
    0x08000576   0x08000576   0x00000002   PAD
    0x08000578   0x08000578   0x00000020   Code   RO          613    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000598   0x08000598   0x00000020   Code   RO          615    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080005b8   0x080005b8   0x000000d4   Code   RO          623    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x0800068c   0x0800068c   0x00000002   Code   RO           70    i.SVC_Handler       stm32f10x_it.o
    0x0800068e   0x0800068e   0x00000008   Code   RO          403    i.SetSysClock       system_stm32f10x.o
    0x08000696   0x08000696   0x00000002   PAD
    0x08000698   0x08000698   0x000000e0   Code   RO          404    i.SetSysClockTo72   system_stm32f10x.o
    0x08000778   0x08000778   0x00000002   Code   RO           71    i.SysTick_Handler   stm32f10x_it.o
    0x0800077a   0x0800077a   0x00000002   PAD
    0x0800077c   0x0800077c   0x00000060   Code   RO          406    i.SystemInit        system_stm32f10x.o
    0x080007dc   0x080007dc   0x000000dc   Code   RO          294    i.USART1_IRQHandler  usart.o
    0x080008b8   0x080008b8   0x00000094   Code   RO          295    i.USART2_IRQHandler  usart.o
    0x0800094c   0x0800094c   0x0000001e   Code   RO          812    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x0800096a   0x0800096a   0x00000018   Code   RO          815    i.USART_Cmd         stm32f10x_usart.o
    0x08000982   0x08000982   0x00000054   Code   RO          819    i.USART_GetITStatus  stm32f10x_usart.o
    0x080009d6   0x080009d6   0x0000004a   Code   RO          821    i.USART_ITConfig    stm32f10x_usart.o
    0x08000a20   0x08000a20   0x000000d8   Code   RO          822    i.USART_Init        stm32f10x_usart.o
    0x08000af8   0x08000af8   0x00000004   Code   RO           72    i.UsageFault_Handler  stm32f10x_it.o
    0x08000afc   0x08000afc   0x0000000e   Code   RO         1050    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08000b0a   0x08000b0a   0x00000002   Code   RO         1051    i.__scatterload_null  mc_w.l(handlers.o)
    0x08000b0c   0x08000b0c   0x0000000e   Code   RO         1052    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08000b1a   0x08000b1a   0x00000002   PAD
    0x08000b1c   0x08000b1c   0x00000024   Code   RO          355    i.board_init        board.o
    0x08000b40   0x08000b40   0x00000028   Code   RO          356    i.clock_init        board.o
    0x08000b68   0x08000b68   0x00000060   Code   RO          480    i.delay_ms          delay.o
    0x08000bc8   0x08000bc8   0x00000026   Code   RO          437    i.fifo_deQueue      fifo.o
    0x08000bee   0x08000bee   0x00000022   Code   RO          438    i.fifo_enQueue      fifo.o
    0x08000c10   0x08000c10   0x0000000c   Code   RO          439    i.fifo_initQueue    fifo.o
    0x08000c1c   0x08000c1c   0x0000002e   Code   RO          441    i.fifo_queueLength  fifo.o
    0x08000c4a   0x08000c4a   0x00000002   PAD
    0x08000c4c   0x08000c4c   0x00000100   Code   RO            1    i.main              main.o
    0x08000d4c   0x08000d4c   0x0000003a   Code   RO          357    i.nvic_init         board.o
    0x08000d86   0x08000d86   0x00000002   PAD
    0x08000d88   0x08000d88   0x0000003c   Code   RO          296    i.usart1_SendByte   usart.o
    0x08000dc4   0x08000dc4   0x0000002c   Code   RO          297    i.usart1_SendCmd    usart.o
    0x08000df0   0x08000df0   0x0000003c   Code   RO          298    i.usart2_SendByte   usart.o
    0x08000e2c   0x08000e2c   0x0000002c   Code   RO          299    i.usart2_SendCmd    usart.o
    0x08000e58   0x08000e58   0x0000010c   Code   RO          358    i.usart_init        board.o
    0x08000f64   0x08000f64   0x00000020   Data   RO         1048    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08000f84, Size: 0x00000bb8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08000f84   0x00000006   Data   RW          303    .data               usart.o
    0x20000006   0x08000f8a   0x00000002   PAD
    0x20000008   0x08000f8c   0x00000014   Data   RW          407    .data               system_stm32f10x.o
    0x2000001c   0x08000fa0   0x00000014   Data   RW          643    .data               stm32f10x_rcc.o
    0x20000030        -       0x00000180   Zero   RW          302    .bss                usart.o
    0x200001b0        -       0x00000204   Zero   RW          442    .bss                fifo.o
    0x200003b4   0x08000fb4   0x00000004   PAD
    0x200003b8        -       0x00000800   Zero   RW          396    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       402         26          0          0          0       2139   board.o
         0          0          0          0          0         32   core_cm3.o
        96          6          0          0          0        991   delay.o
       426          0          0          0          0       5328   emm_v5.o
       130          0          0          0        516       3252   fifo.o
       256          6          0          0          0     231907   main.o
       132         22          0          0          0       1867   misc.o
        36          8        304          0       2048        808   startup_stm32f10x_hd.o
       422          6          0          0          0       3126   stm32f10x_gpio.o
        26          0          0          0          0       4078   stm32f10x_it.o
       276         32          0         20          0       4870   stm32f10x_rcc.o
       428          6          0          0          0       5604   stm32f10x_usart.o
       328         28          0         20          0       2425   system_stm32f10x.o
       576         64          0          6        384       4428   usart.o

    ----------------------------------------------------------------------
      3548        <USER>        <GROUP>         48       2952     270855   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          2          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o

    ----------------------------------------------------------------------
        88         <USER>          <GROUP>          0          0         68   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        86         16          0          0          0         68   mc_w.l

    ----------------------------------------------------------------------
        88         <USER>          <GROUP>          0          0         68   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3636        220        336         48       2952     268839   Grand Totals
      3636        220        336         48       2952     268839   ELF Image Totals
      3636        220        336         48          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3972 (   3.88kB)
    Total RW  Size (RW Data + ZI Data)              3000 (   2.93kB)
    Total ROM Size (Code + RO Data + RW Data)       4020 (   3.93kB)

==============================================================================

