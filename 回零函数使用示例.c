#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"
#include "电机回零函数库.h"

/**********************************************************
***	回零函数使用示例程序（简化版）
***	演示三个核心回零函数的使用方法
***	解决电机2同步回零问题
**********************************************************/

/**
 * @brief 基础回零示例（简化版）
 */
void basic_return_zero_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);

    // 设置初始零点
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);

    // 电机2使能（尝试多个地址）
    Emm_V5_En_Control_USART(2, 1, true, false);
    Emm_V5_En_Control_USART(2, 2, true, false);
    delay_ms(100);

    // 复位位置
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 2);
    delay_ms(200);

    // 移动电机到不同位置
    Emm_V5_Pos_Control(1, 0, 800, 10, 1600, false, false);        // 电机1移动180°
    Emm_V5_Pos_Control_USART(2, 1, 1, 800, 10, 2400, false, false); // 电机2移动270°（地址1）
    Emm_V5_Pos_Control_USART(2, 2, 1, 800, 10, 2400, false, false); // 电机2移动270°（地址2）
    delay_ms(3000);

    // 使用简化回零函数
    Motor1_Return_To_Zero();  // 电机1回零
    delay_ms(500);

    Motor2_Return_To_Zero();  // 电机2回零（自动解决地址问题）
    delay_ms(500);
}

/**
 * @brief 同步回零示例（解决电机2问题的版本）
 */
void sync_return_zero_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);

    // 设置零点
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);

    // 电机2使能（多地址尝试）
    Emm_V5_En_Control_USART(2, 1, true, false);
    Emm_V5_En_Control_USART(2, 2, true, false);
    delay_ms(100);

    // 复位位置
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 2);
    delay_ms(200);

    // 移动电机到不同位置
    Emm_V5_Pos_Control(1, 0, 1000, 15, 2400, false, false);        // 电机1移动270°
    Emm_V5_Pos_Control_USART(2, 1, 1, 1000, 15, 1600, false, false); // 电机2移动180°（地址1）
    Emm_V5_Pos_Control_USART(2, 2, 1, 1000, 15, 1600, false, false); // 电机2移动180°（地址2）
    delay_ms(3000);

    // 使用同步回零函数（解决电机2问题）
    Dual_Motor_Sync_Return_To_Zero();
    delay_ms(1000);
}

/**
 * @brief 主函数 - 简化版示例程序
 */
int main(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 设置零点
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    delay_ms(200);
    
    // 选择要运行的示例（取消注释对应的函数）

    // basic_return_zero_example();        // 基础回零示例
    sync_return_zero_example();            // 同步回零示例（推荐）

    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	简化版使用说明：
***
***	1. 基础使用：
***	   Motor1_Return_To_Zero();     // 电机1回零
***	   Motor2_Return_To_Zero();     // 电机2回零（自动解决地址问题）
***	   Dual_Motor_Sync_Return_To_Zero(); // 双电机同步回零
***
***	2. 便捷宏使用：
***	   MOTOR1_RETURN_ZERO();        // 电机1回零
***	   MOTOR2_RETURN_ZERO();        // 电机2回零
***	   DUAL_MOTOR_RETURN_ZERO();    // 双电机同步回零
***
***	注意事项：
***	- 使用前确保已正确设置零点位置
***	- 电机2函数已自动解决地址问题
***	- 同步回零函数已解决电机2同步问题
**********************************************************/


{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 设置零点
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    delay_ms(200);
    
    // 测试不同距离的智能回零
    uint32_t test_positions[] = {400, 1600, 4800, 9600};  // 45°, 180°, 1.5圈, 3圈
    
    for(int i = 0; i < 4; i++)
    {
        // 移动到测试位置
        Emm_V5_Pos_Control(1, 0, 1000, 15, test_positions[i], false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, 1000, 15, test_positions[i], false, false);
        delay_ms(6000);  // 等待移动完成
        
        // 智能回零（自动选择最优参数）
        Smart_Return_To_Zero(1, test_positions[i]);  // 电机1
        delay_ms(500);
        Smart_Return_To_Zero(2, test_positions[i]);  // 电机2
        delay_ms(1000);
    }
}

/**
 * @brief 电机2多地址回零示例（解决地址问题）
 */
void motor2_multi_address_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 电机1正常回零
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    Emm_V5_Reset_CurPos_To_Zero(1);
    delay_ms(200);
    
    Emm_V5_Pos_Control(1, 0, 800, 10, 1600, false, false);  // 移动180°
    delay_ms(2000);
    Motor1_Return_To_Zero();  // 正常回零
    delay_ms(1000);
    
    // 电机2多地址尝试回零
    Motor2_Multi_Address_Return_To_Zero();  // 尝试多个地址
    delay_ms(2000);
}

/**
 * @brief 安全回零示例
 */
void safe_return_zero_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 设置零点
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    delay_ms(200);
    
    // 移动电机
    Emm_V5_Pos_Control(1, 0, 800, 10, 1600, false, false);
    Emm_V5_Pos_Control_USART(2, 1, 1, 800, 10, 1600, false, false);
    delay_ms(2000);
    
    // 安全回零（带错误检查）
    bool result1 = Safe_Return_To_Zero(1);  // 电机1安全回零
    bool result2 = Safe_Return_To_Zero(2);  // 电机2安全回零
    
    // 根据结果进行处理
    if(result1 && result2) {
        // 两个电机都成功回零
        delay_ms(1000);
    } else {
        // 有电机回零失败，进行错误处理
        delay_ms(2000);
    }
}

/**
 * @brief 主函数 - 选择示例程序
 */
int main(void)
{
    // 选择要运行的示例（取消注释对应的函数）
    
    basic_return_zero_example();        // 基础回零示例（推荐先运行）
    // speed_return_zero_example();       // 快速/慢速回零示例
    // param_return_zero_example();       // 带参数回零示例
    // sync_return_zero_example();        // 同步回零示例
    // smart_return_zero_example();       // 智能回零示例
    // motor2_multi_address_example();    // 电机2多地址示例
    // safe_return_zero_example();        // 安全回零示例
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	使用说明：
***
***	1. 基础使用：
***	   Motor1_Return_To_Zero();     // 电机1回零
***	   Motor2_Return_To_Zero();     // 电机2回零
***
***	2. 便捷宏使用：
***	   MOTOR1_RETURN_ZERO();        // 电机1回零
***	   MOTOR2_RETURN_ZERO();        // 电机2回零
***	   DUAL_MOTOR_RETURN_ZERO();    // 双电机同步回零
***
***	3. 高级功能：
***	   Motor1_Fast_Return_To_Zero();              // 快速回零
***	   Motor2_Slow_Return_To_Zero();              // 慢速回零
***	   Motor1_Return_To_Zero_WithParams(600, 8);  // 自定义参数
***	   Smart_Return_To_Zero(1, 1600);             // 智能回零
***
***	4. 同步回零：
***	   COMPLETE_SYNC_RETURN_ZERO();  // 完整同步回零
***
***	5. 问题解决：
***	   Motor2_Multi_Address_Return_To_Zero();  // 解决电机2地址问题
***	   Safe_Return_To_Zero(1);                 // 带错误检查的回零
***
***	注意事项：
***	- 使用前确保已正确设置零点位置
***	- 回零前确保电机已使能
***	- 根据实际距离选择合适的等待时间
***	- 电机2如有问题，优先使用多地址尝试函数
**********************************************************/
