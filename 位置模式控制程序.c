#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	双电机位置模式控制程序
***	功能：电机1顺时针，电机2逆时针，各旋转180°每秒
***	硬件：STM32F103 + 两个Emm_V5.0驱动器
**********************************************************/

// 位置模式参数定义
#define STEPS_PER_REVOLUTION    3200    // 每圈脉冲数（根据驱动器细分设置）
#define STEPS_180_DEGREE        1600    // 180°对应的脉冲数 (3200/2)
#define SPEED_180_PER_SEC       1600    // 180°每秒对应的RPM (1600RPM)
#define ACCELERATION            15      // 加速度参数

/**
 * @brief 位置模式基本逻辑演示
 */
void position_mode_basic_demo(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);  // 等待驱动器初始化
    
    // 2. 使能两个电机
    Emm_V5_En_Control(1, true, false);           // 使能电机1
    Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机2
    delay_ms(100);
    
    // 3. 第一次位置运动：电机1顺时针180°，电机2逆时针180°
    Emm_V5_Pos_Control(1, 0, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
    Emm_V5_Pos_Control_USART(2, 1, 1, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
    
    delay_ms(1200);  // 等待运动完成（180°约需1秒，加上加减速时间）
    
    // 4. 循环演示
    for(int cycle = 0; cycle < 5; cycle++)
    {
        delay_ms(2000);  // 间隔2秒
        
        // 继续相同方向运动180°
        Emm_V5_Pos_Control(1, 0, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        
        delay_ms(1200);  // 等待运动完成
    }
    
    // 5. 停止并禁用电机
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(500);
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 位置模式往返运动演示
 */
void position_mode_reciprocating_demo(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 往返运动循环
    for(int cycle = 0; cycle < 3; cycle++)
    {
        // 正向运动：电机1顺时针180°，电机2逆时针180°
        Emm_V5_Pos_Control(1, 0, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        delay_ms(1200);
        
        delay_ms(1000);  // 停顿1秒
        
        // 反向运动：电机1逆时针180°，电机2顺时针180°
        Emm_V5_Pos_Control(1, 1, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 0, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        delay_ms(1200);
        
        delay_ms(1000);  // 停顿1秒
    }
    
    // 4. 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 位置模式同步运动演示
 */
void position_mode_sync_demo(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 同步位置运动
    for(int cycle = 0; cycle < 4; cycle++)
    {
        // 设置同步位置运动（snF=true）
        Emm_V5_Pos_Control(1, 0, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, true);
        Emm_V5_Pos_Control_USART(2, 1, 1, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, true);
        
        delay_ms(100);  // 短暂延时确保命令发送完成
        
        // 触发同步运动
        Emm_V5_Synchronous_motion(1);
        delay_ms(1200);  // 等待运动完成
        
        delay_ms(2000);  // 间隔2秒
    }
    
    // 4. 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 位置模式多角度测试
 */
void position_mode_multi_angle_demo(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 不同角度测试
    uint16_t angles[] = {90, 180, 270, 360};  // 角度数组
    uint32_t steps[] = {800, 1600, 2400, 3200};  // 对应脉冲数
    uint16_t speeds[] = {800, 1600, 2400, 3200};  // 对应速度(RPM)
    
    for(int i = 0; i < 4; i++)
    {
        // 电机1顺时针，电机2逆时针
        Emm_V5_Pos_Control(1, 0, speeds[i], ACCELERATION, steps[i], false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, speeds[i], ACCELERATION, steps[i], false, false);
        
        // 等待运动完成（根据角度调整等待时间）
        delay_ms(1000 + angles[i] * 2);
        
        delay_ms(1500);  // 间隔1.5秒
        
        // 复位位置
        Emm_V5_Reset_CurPos_To_Zero(1);
        Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
        delay_ms(100);
    }
    
    // 4. 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 位置模式精确控制演示
 */
void position_mode_precise_demo(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 精确180°运动，每秒一次
    for(int i = 0; i < 10; i++)
    {
        // 精确180°运动
        Emm_V5_Pos_Control(1, 0, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        Emm_V5_Pos_Control_USART(2, 1, 1, SPEED_180_PER_SEC, ACCELERATION, STEPS_180_DEGREE, false, false);
        
        // 精确等待1秒（180°运动时间）
        delay_ms(1000);
        
        // 短暂停顿
        delay_ms(200);
    }
    
    // 4. 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 主函数 - 选择演示程序
 */
int main(void)
{
    // 选择要运行的演示程序（取消注释对应的函数）
    
    position_mode_basic_demo();           // 基本位置控制演示
    // position_mode_reciprocating_demo();  // 往返运动演示
    // position_mode_sync_demo();           // 同步运动演示
    // position_mode_multi_angle_demo();    // 多角度测试
    // position_mode_precise_demo();        // 精确控制演示
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	位置模式控制说明
***
***	1. 基本参数：
***	   - 每圈脉冲数：3200（根据驱动器细分设置）
***	   - 180°脉冲数：1600
***	   - 180°每秒速度：1600RPM
***
***	2. 函数参数说明：
***	   Emm_V5_Pos_Control_USART(串口号, 地址, 方向, 速度, 加速度, 脉冲数, 相对位置, 同步标志)
***
***	3. 关键逻辑：
***	   - 位置模式会自动停止，无需手动停止
***	   - 运动时间 = 脉冲数 / (速度RPM * 脉冲数每圈 / 60)
***	   - 180°运动时间约1秒（不含加减速时间）
***
***	4. 注意事项：
***	   - 使能电机后才能进行位置控制
***	   - 等待时间要考虑加减速过程
***	   - 可以使用复位位置功能避免累积误差
**********************************************************/
