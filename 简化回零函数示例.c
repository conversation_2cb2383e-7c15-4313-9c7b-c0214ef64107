#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"
#include "电机回零函数库.h"

/**********************************************************
***	简化回零函数示例程序
***	只保留三个核心函数，解决电机2同步回零问题
**********************************************************/

/**
 * @brief 基础回零示例
 */
void basic_return_zero_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 设置初始零点
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 电机2使能（多地址尝试）
    Emm_V5_En_Control_USART(2, 1, true, false);
    Emm_V5_En_Control_USART(2, 2, true, false);
    delay_ms(100);
    
    // 复位位置
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 2);
    delay_ms(200);
    
    // 移动电机到不同位置
    Emm_V5_Pos_Control(1, 0, 800, 10, 1600, false, false);        // 电机1移动180°
    Emm_V5_Pos_Control_USART(2, 1, 1, 800, 10, 2400, false, false); // 电机2移动270°（地址1）
    Emm_V5_Pos_Control_USART(2, 2, 1, 800, 10, 2400, false, false); // 电机2移动270°（地址2）
    delay_ms(3000);
    
    // 使用简化回零函数
    Motor1_Return_To_Zero();  // 电机1回零
    delay_ms(500);
    
    Motor2_Return_To_Zero();  // 电机2回零（自动解决地址问题）
    delay_ms(500);
}

/**
 * @brief 同步回零示例（解决电机2问题的版本）
 */
void sync_return_zero_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 设置零点
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 电机2使能（多地址尝试）
    Emm_V5_En_Control_USART(2, 1, true, false);
    Emm_V5_En_Control_USART(2, 2, true, false);
    delay_ms(100);
    
    // 复位位置
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 2);
    delay_ms(200);
    
    // 移动电机到不同位置
    Emm_V5_Pos_Control(1, 0, 1000, 15, 2400, false, false);        // 电机1移动270°
    Emm_V5_Pos_Control_USART(2, 1, 1, 1000, 15, 1600, false, false); // 电机2移动180°（地址1）
    Emm_V5_Pos_Control_USART(2, 2, 1, 1000, 15, 1600, false, false); // 电机2移动180°（地址2）
    delay_ms(3000);
    
    // 使用同步回零函数（解决电机2问题）
    Dual_Motor_Sync_Return_To_Zero();
    delay_ms(1000);
}

/**
 * @brief 主函数 - 简化版示例程序
 */
int main(void)
{
    // 选择要运行的示例（取消注释对应的函数）
    
    basic_return_zero_example();        // 基础回零示例（推荐先运行）
    // sync_return_zero_example();        // 同步回零示例
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	简化版使用说明：
***
***	1. 基础使用：
***	   Motor1_Return_To_Zero();     // 电机1回零
***	   Motor2_Return_To_Zero();     // 电机2回零（自动解决地址问题）
***	   Dual_Motor_Sync_Return_To_Zero(); // 双电机同步回零
***
***	2. 便捷宏使用：
***	   MOTOR1_RETURN_ZERO();        // 电机1回零
***	   MOTOR2_RETURN_ZERO();        // 电机2回零
***	   DUAL_MOTOR_RETURN_ZERO();    // 双电机同步回零
***
***	注意事项：
***	- 使用前确保已正确设置零点位置
***	- 电机2函数已自动解决地址问题
***	- 同步回零函数已解决电机2同步问题
***	- 所有函数都已优化，只保留有效方法
**********************************************************/
