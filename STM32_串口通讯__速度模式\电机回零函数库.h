#ifndef __MOTOR_RETURN_ZERO_H
#define __MOTOR_RETURN_ZERO_H

#include "stm32f10x.h"
#include <stdbool.h>

/**********************************************************
***	电机1和电机2回零函数库头文件
***	声明所有回零相关函数
**********************************************************/

// 回零参数配置宏定义
#define DEFAULT_RETURN_SPEED        800     // 默认回零速度 (RPM)
#define DEFAULT_RETURN_ACCELERATION 10      // 默认回零加速度
#define SLOW_RETURN_SPEED          400     // 慢速回零速度 (RPM)
#define FAST_RETURN_SPEED          1200    // 快速回零速度 (RPM)
#define HIGH_ACCELERATION          20      // 高加速度
#define LOW_ACCELERATION           5       // 低加速度

/**********************************************************
***	电机1回零函数声明
**********************************************************/

/**
 * @brief 电机1基础回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Return_To_Zero(void);

/**
 * @brief 电机1带参数回零函数
 * @param speed 回零速度 (RPM)，范围100-3000
 * @param acceleration 回零加速度，范围3-50
 * @retval 无
 */
void Motor1_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration);

/**
 * @brief 电机1快速回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Fast_Return_To_Zero(void);

/**
 * @brief 电机1慢速精确回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Slow_Return_To_Zero(void);

/**
 * @brief 电机1同步回零函数（设置同步标志）
 * @param 无
 * @retval 无
 * @note 需要调用Emm_V5_Synchronous_motion()触发同步运动
 */
void Motor1_Sync_Return_To_Zero(void);

/**********************************************************
***	电机2回零函数声明
**********************************************************/

/**
 * @brief 电机2基础回零函数
 * @param 无
 * @retval 无
 */
void Motor2_Return_To_Zero(void);

/**
 * @brief 电机2带参数回零函数
 * @param speed 回零速度 (RPM)，范围100-3000
 * @param acceleration 回零加速度，范围3-50
 * @retval 无
 */
void Motor2_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration);

/**
 * @brief 电机2快速回零函数
 * @param 无
 * @retval 无
 */
void Motor2_Fast_Return_To_Zero(void);

/**
 * @brief 电机2慢速精确回零函数
 * @param 无
 * @retval 无
 */
void Motor2_Slow_Return_To_Zero(void);

/**
 * @brief 电机2同步回零函数（设置同步标志）
 * @param 无
 * @retval 无
 * @note 需要调用Emm_V5_Synchronous_motion()触发同步运动
 */
void Motor2_Sync_Return_To_Zero(void);

/**
 * @brief 电机2多地址尝试回零函数（解决地址问题）
 * @param 无
 * @retval 无
 * @note 尝试地址1-5，解决电机地址设置问题
 */
void Motor2_Multi_Address_Return_To_Zero(void);

/**********************************************************
***	双电机回零函数声明
**********************************************************/

/**
 * @brief 双电机独立回零函数
 * @param 无
 * @retval 无
 * @note 先回零电机1，再回零电机2
 */
void Dual_Motor_Independent_Return_To_Zero(void);

/**
 * @brief 双电机同步回零函数
 * @param 无
 * @retval 无
 * @note 两个电机同时开始回零运动
 */
void Dual_Motor_Sync_Return_To_Zero(void);

/**
 * @brief 双电机带参数同步回零函数
 * @param speed 回零速度 (RPM)，范围100-3000
 * @param acceleration 回零加速度，范围3-50
 * @retval 无
 */
void Dual_Motor_Sync_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration);

/**********************************************************
***	高级回零函数声明
**********************************************************/

/**
 * @brief 智能回零函数（自动选择最优参数）
 * @param motor_num 电机编号 (1或2)
 * @param current_position 当前位置（脉冲数，估算值）
 * @retval 无
 * @note 根据距离自动选择合适的速度和加速度
 */
void Smart_Return_To_Zero(uint8_t motor_num, uint32_t current_position);

/**
 * @brief 安全回零函数（带错误检查）
 * @param motor_num 电机编号 (1或2)
 * @retval 回零是否成功 (true=成功, false=失败)
 */
bool Safe_Return_To_Zero(uint8_t motor_num);

/**********************************************************
***	使用示例函数声明
**********************************************************/

/**
 * @brief 回零函数使用示例
 * @param 无
 * @retval 无
 * @note 演示各种回零函数的使用方法
 */
void Return_To_Zero_Examples(void);

/**********************************************************
***	便捷宏定义
**********************************************************/

// 快速调用宏
#define MOTOR1_RETURN_ZERO()        Motor1_Return_To_Zero()
#define MOTOR2_RETURN_ZERO()        Motor2_Return_To_Zero()
#define DUAL_MOTOR_RETURN_ZERO()    Dual_Motor_Sync_Return_To_Zero()

// 带参数调用宏
#define MOTOR1_FAST_RETURN()        Motor1_Fast_Return_To_Zero()
#define MOTOR1_SLOW_RETURN()        Motor1_Slow_Return_To_Zero()
#define MOTOR2_FAST_RETURN()        Motor2_Fast_Return_To_Zero()
#define MOTOR2_SLOW_RETURN()        Motor2_Slow_Return_To_Zero()

// 同步回零宏
#define SYNC_RETURN_ZERO_START()    do { \
                                        Motor1_Sync_Return_To_Zero(); \
                                        Motor2_Sync_Return_To_Zero(); \
                                        delay_ms(100); \
                                    } while(0)

#define SYNC_RETURN_ZERO_TRIGGER()  do { \
                                        Emm_V5_Synchronous_motion(1); \
                                        delay_ms(2000); \
                                    } while(0)

// 完整同步回零宏
#define COMPLETE_SYNC_RETURN_ZERO() do { \
                                        SYNC_RETURN_ZERO_START(); \
                                        SYNC_RETURN_ZERO_TRIGGER(); \
                                    } while(0)

/**********************************************************
***	错误代码定义
**********************************************************/
typedef enum {
    RETURN_ZERO_SUCCESS = 0,        // 回零成功
    RETURN_ZERO_INVALID_MOTOR,      // 无效的电机编号
    RETURN_ZERO_TIMEOUT,            // 回零超时
    RETURN_ZERO_POSITION_ERROR,     // 位置错误
    RETURN_ZERO_COMMUNICATION_ERROR // 通信错误
} ReturnZeroResult_t;

/**********************************************************
***	回零状态定义
**********************************************************/
typedef enum {
    RETURN_ZERO_IDLE = 0,           // 空闲状态
    RETURN_ZERO_IN_PROGRESS,        // 回零进行中
    RETURN_ZERO_COMPLETED,          // 回零完成
    RETURN_ZERO_FAILED              // 回零失败
} ReturnZeroState_t;

/**********************************************************
***	回零配置结构体
**********************************************************/
typedef struct {
    uint16_t speed;                 // 回零速度 (RPM)
    uint8_t acceleration;           // 回零加速度
    uint16_t timeout_ms;            // 超时时间 (ms)
    bool enable_check;              // 是否启用完成检查
    ReturnZeroState_t state;        // 当前状态
} ReturnZeroConfig_t;

// 默认配置
extern const ReturnZeroConfig_t DEFAULT_RETURN_CONFIG;
extern const ReturnZeroConfig_t FAST_RETURN_CONFIG;
extern const ReturnZeroConfig_t SLOW_RETURN_CONFIG;

#endif /* __MOTOR_RETURN_ZERO_H */

/**********************************************************
***	使用说明：
***
***	1. 包含头文件：
***	   #include "电机回零函数库.h"
***
***	2. 基本使用：
***	   Motor1_Return_To_Zero();     // 电机1回零
***	   Motor2_Return_To_Zero();     // 电机2回零
***
***	3. 便捷宏使用：
***	   MOTOR1_RETURN_ZERO();        // 电机1回零
***	   MOTOR2_RETURN_ZERO();        // 电机2回零
***	   DUAL_MOTOR_RETURN_ZERO();    // 双电机同步回零
***
***	4. 高级功能：
***	   Motor1_Return_To_Zero_WithParams(600, 8);  // 自定义参数
***	   Smart_Return_To_Zero(1, 1600);             // 智能回零
***	   Safe_Return_To_Zero(2);                    // 安全回零
***
***	5. 同步回零：
***	   COMPLETE_SYNC_RETURN_ZERO();  // 完整同步回零
***
***	   或者分步执行：
***	   SYNC_RETURN_ZERO_START();     // 设置同步参数
***	   SYNC_RETURN_ZERO_TRIGGER();   // 触发同步运动
**********************************************************/
