Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.dual_motor_return_to_zero) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    main.o(i.dual_motor_return_to_zero) refers to emm_v5.o(i.Emm_V5_En_Control_USART) for Emm_V5_En_Control_USART
    main.o(i.dual_motor_return_to_zero) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.dual_motor_return_to_zero) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    main.o(i.dual_motor_return_to_zero) refers to emm_v5.o(i.Emm_V5_Pos_Control_USART) for Emm_V5_Pos_Control_USART
    main.o(i.dual_motor_return_to_zero) refers to emm_v5.o(i.Emm_V5_Synchronous_motion) for Emm_V5_Synchronous_motion
    main.o(i.main) refers to board.o(i.board_init) for board_init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_En_Control_USART) for Emm_V5_En_Control_USART
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero_USART) for Emm_V5_Reset_CurPos_To_Zero_USART
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Origin_Set_O_USART) for Emm_V5_Origin_Set_O_USART
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Pos_Control_USART) for Emm_V5_Pos_Control_USART
    main.o(i.main) refers to main.o(i.dual_motor_return_to_zero) for dual_motor_return_to_zero
    main.o(i.main) refers to usart.o(.data) for rxFrameFlag
    main.o(i.motor1_return_to_zero) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    main.o(i.motor1_return_to_zero) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.motor1_return_to_zero) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    main.o(i.motor2_return_to_zero) refers to emm_v5.o(i.Emm_V5_En_Control_USART) for Emm_V5_En_Control_USART
    main.o(i.motor2_return_to_zero) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.motor2_return_to_zero) refers to emm_v5.o(i.Emm_V5_Pos_Control_USART) for Emm_V5_Pos_Control_USART
    emm_v5.o(i.Emm_V5_En_Control) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_En_Control_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Interrupt_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Modify_Params_USART) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Set_O_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Pos_Control) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Pos_Control_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Read_Sys_Params_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_SendCmd_USART) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_SendCmd_USART) refers to usart.o(i.usart2_SendCmd) for usart2_SendCmd
    emm_v5.o(i.Emm_V5_Stop_Now) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Stop_Now_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Synchronous_motion_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    emm_v5.o(i.Emm_V5_Vel_Control_USART) refers to emm_v5.o(i.Emm_V5_SendCmd_USART) for Emm_V5_SendCmd_USART
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_enQueue) for fifo_enQueue
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_deQueue) for fifo_deQueue
    usart.o(i.USART1_IRQHandler) refers to fifo.o(.bss) for rxFIFO1
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for rxCount1
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for rxCmd1
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_enQueue) for fifo_enQueue
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    usart.o(i.USART2_IRQHandler) refers to fifo.o(i.fifo_deQueue) for fifo_deQueue
    usart.o(i.USART2_IRQHandler) refers to fifo.o(.bss) for rxFIFO2
    usart.o(i.USART2_IRQHandler) refers to usart.o(.data) for rxCount2
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for rxCmd2
    usart.o(i.usart1_SendCmd) refers to usart.o(i.usart1_SendByte) for usart1_SendByte
    usart.o(i.usart2_SendCmd) refers to usart.o(i.usart2_SendByte) for usart2_SendByte
    usart.o(i.usart_SendByte) refers to usart.o(i.usart1_SendByte) for usart1_SendByte
    usart.o(i.usart_SendCmd) refers to usart.o(i.usart1_SendCmd) for usart1_SendCmd
    board.o(i.board_init) refers to board.o(i.nvic_init) for nvic_init
    board.o(i.board_init) refers to board.o(i.clock_init) for clock_init
    board.o(i.board_init) refers to fifo.o(i.fifo_initQueue) for fifo_initQueue
    board.o(i.board_init) refers to board.o(i.usart_init) for usart_init
    board.o(i.board_init) refers to fifo.o(.bss) for rxFIFO1
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    board.o(i.clock_init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    board.o(i.usart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    delay.o(i.delay_ms) refers to system_stm32f10x.o(.data) for SystemCoreClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(i.motor1_return_to_zero), (52 bytes).
    Removing main.o(i.motor2_return_to_zero), (108 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (58 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode_USART), (66 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt_USART), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (170 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params_USART), (176 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return_USART), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (262 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params_USART), (270 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro_USART), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Stop_Now), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Stop_Now_USART), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion_USART), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (78 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control_USART), (80 bytes).
    Removing usart.o(i.usart_SendByte), (12 bytes).
    Removing usart.o(i.usart_SendCmd), (16 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing fifo.o(i.fifo_isEmpty), (22 bytes).
    Removing delay.o(i.delay_cnt), (10 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).

99 unused section(s) (total 4556 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ..\APP\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\APP\stm32f10x_it.c                    0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\BSP\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\BSP\board.c                           0x00000000   Number         0  board.o ABSOLUTE
    ..\BSP\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\DRIVERS\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    ..\DRIVERS\fifo.c                        0x00000000   Number         0  fifo.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section       36  init.o(.text)
    i.BusFault_Handler                       0x0800018c   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000190   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x08000192   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_En_Control_USART                0x080001cc   Section        0  emm_v5.o(i.Emm_V5_En_Control_USART)
    i.Emm_V5_Origin_Set_O_USART              0x0800020e   Section        0  emm_v5.o(i.Emm_V5_Origin_Set_O_USART)
    i.Emm_V5_Pos_Control                     0x08000246   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Pos_Control_USART               0x080002b2   Section        0  emm_v5.o(i.Emm_V5_Pos_Control_USART)
    i.Emm_V5_Reset_CurPos_To_Zero            0x08000322   Section        0  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    i.Emm_V5_Reset_CurPos_To_Zero_USART      0x08000350   Section        0  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero_USART)
    i.Emm_V5_SendCmd_USART                   0x08000382   Section        0  emm_v5.o(i.Emm_V5_SendCmd_USART)
    Emm_V5_SendCmd_USART                     0x08000383   Thumb Code    36  emm_v5.o(i.Emm_V5_SendCmd_USART)
    i.Emm_V5_Synchronous_motion              0x080003a6   Section        0  emm_v5.o(i.Emm_V5_Synchronous_motion)
    i.GPIO_Init                              0x080003d4   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x080004ec   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.HardFault_Handler                      0x0800057c   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08000580   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000584   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000588   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x080005f8   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x0800060c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000610   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000630   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000650   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08000724   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08000726   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08000727   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000730   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000731   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000810   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000814   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_IRQHandler                      0x08000874   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08000950   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART_ClearITPendingBit                0x080009e4   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08000a02   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08000a1a   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08000a6e   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08000ab8   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.UsageFault_Handler                     0x08000b90   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x08000b94   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08000ba2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08000ba4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.board_init                             0x08000bb4   Section        0  board.o(i.board_init)
    i.clock_init                             0x08000bd8   Section        0  board.o(i.clock_init)
    i.delay_ms                               0x08000c00   Section        0  delay.o(i.delay_ms)
    i.dual_motor_return_to_zero              0x08000c60   Section        0  main.o(i.dual_motor_return_to_zero)
    i.fifo_deQueue                           0x08000cc4   Section        0  fifo.o(i.fifo_deQueue)
    i.fifo_enQueue                           0x08000cea   Section        0  fifo.o(i.fifo_enQueue)
    i.fifo_initQueue                         0x08000d0c   Section        0  fifo.o(i.fifo_initQueue)
    i.fifo_queueLength                       0x08000d18   Section        0  fifo.o(i.fifo_queueLength)
    i.main                                   0x08000d48   Section        0  main.o(i.main)
    i.nvic_init                              0x08000e70   Section        0  board.o(i.nvic_init)
    i.usart1_SendByte                        0x08000eac   Section        0  usart.o(i.usart1_SendByte)
    i.usart1_SendCmd                         0x08000ee8   Section        0  usart.o(i.usart1_SendCmd)
    i.usart2_SendByte                        0x08000f14   Section        0  usart.o(i.usart2_SendByte)
    i.usart2_SendCmd                         0x08000f50   Section        0  usart.o(i.usart2_SendCmd)
    i.usart_init                             0x08000f7c   Section        0  board.o(i.usart_init)
    .data                                    0x20000000   Section        6  usart.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f10x.o(.data)
    .data                                    0x2000001c   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000001c   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000002c   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x20000030   Section      384  usart.o(.bss)
    .bss                                     0x200001b0   Section      516  fifo.o(.bss)
    STACK                                    0x200003b8   Section     2048  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __scatterload                            0x08000169   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000169   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x0800018d   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000191   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x08000193   Thumb Code    58  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_En_Control_USART                  0x080001cd   Thumb Code    66  emm_v5.o(i.Emm_V5_En_Control_USART)
    Emm_V5_Origin_Set_O_USART                0x0800020f   Thumb Code    56  emm_v5.o(i.Emm_V5_Origin_Set_O_USART)
    Emm_V5_Pos_Control                       0x08000247   Thumb Code   108  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Pos_Control_USART                 0x080002b3   Thumb Code   112  emm_v5.o(i.Emm_V5_Pos_Control_USART)
    Emm_V5_Reset_CurPos_To_Zero              0x08000323   Thumb Code    46  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    Emm_V5_Reset_CurPos_To_Zero_USART        0x08000351   Thumb Code    50  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero_USART)
    Emm_V5_Synchronous_motion                0x080003a7   Thumb Code    46  emm_v5.o(i.Emm_V5_Synchronous_motion)
    GPIO_Init                                0x080003d5   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x080004ed   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    HardFault_Handler                        0x0800057d   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08000581   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08000585   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000589   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x080005f9   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x0800060d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000611   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000631   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000651   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08000725   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000811   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08000815   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_IRQHandler                        0x08000875   Thumb Code   186  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08000951   Thumb Code   126  usart.o(i.USART2_IRQHandler)
    USART_ClearITPendingBit                  0x080009e5   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08000a03   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08000a1b   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08000a6f   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08000ab9   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    UsageFault_Handler                       0x08000b91   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08000b95   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08000ba3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08000ba5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    board_init                               0x08000bb5   Thumb Code    28  board.o(i.board_init)
    clock_init                               0x08000bd9   Thumb Code    36  board.o(i.clock_init)
    delay_ms                                 0x08000c01   Thumb Code    90  delay.o(i.delay_ms)
    dual_motor_return_to_zero                0x08000c61   Thumb Code   100  main.o(i.dual_motor_return_to_zero)
    fifo_deQueue                             0x08000cc5   Thumb Code    38  fifo.o(i.fifo_deQueue)
    fifo_enQueue                             0x08000ceb   Thumb Code    34  fifo.o(i.fifo_enQueue)
    fifo_initQueue                           0x08000d0d   Thumb Code    12  fifo.o(i.fifo_initQueue)
    fifo_queueLength                         0x08000d19   Thumb Code    46  fifo.o(i.fifo_queueLength)
    main                                     0x08000d49   Thumb Code   292  main.o(i.main)
    nvic_init                                0x08000e71   Thumb Code    58  board.o(i.nvic_init)
    usart1_SendByte                          0x08000ead   Thumb Code    56  usart.o(i.usart1_SendByte)
    usart1_SendCmd                           0x08000ee9   Thumb Code    44  usart.o(i.usart1_SendCmd)
    usart2_SendByte                          0x08000f15   Thumb Code    56  usart.o(i.usart2_SendByte)
    usart2_SendCmd                           0x08000f51   Thumb Code    44  usart.o(i.usart2_SendCmd)
    usart_init                               0x08000f7d   Thumb Code   254  board.o(i.usart_init)
    Region$$Table$$Base                      0x08001088   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080010a8   Number         0  anon$$obj.o(Region$$Table)
    rxFrameFlag1                             0x20000000   Data           1  usart.o(.data)
    rxCount1                                 0x20000001   Data           1  usart.o(.data)
    rxFrameFlag2                             0x20000002   Data           1  usart.o(.data)
    rxCount2                                 0x20000003   Data           1  usart.o(.data)
    rxFrameFlag                              0x20000004   Data           1  usart.o(.data)
    rxCount                                  0x20000005   Data           1  usart.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f10x.o(.data)
    rxCmd1                                   0x20000030   Data         128  usart.o(.bss)
    rxCmd2                                   0x200000b0   Data         128  usart.o(.bss)
    rxCmd                                    0x20000130   Data         128  usart.o(.bss)
    rxFIFO1                                  0x200001b0   Data         258  fifo.o(.bss)
    rxFIFO2                                  0x200002b2   Data         258  fifo.o(.bss)
    __initial_sp                             0x20000bb8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000010d8, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000010a8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          416    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         1045  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         1050    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         1053    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1055    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1057    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         1058    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1060    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1062    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         1051    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          417    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000024   Code   RO         1064    .text               mc_w.l(init.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO           82    i.BusFault_Handler  stm32f10x_it.o
    0x08000190   0x08000190   0x00000002   Code   RO           83    i.DebugMon_Handler  stm32f10x_it.o
    0x08000192   0x08000192   0x0000003a   Code   RO          145    i.Emm_V5_En_Control  emm_v5.o
    0x080001cc   0x080001cc   0x00000042   Code   RO          146    i.Emm_V5_En_Control_USART  emm_v5.o
    0x0800020e   0x0800020e   0x00000038   Code   RO          154    i.Emm_V5_Origin_Set_O_USART  emm_v5.o
    0x08000246   0x08000246   0x0000006c   Code   RO          157    i.Emm_V5_Pos_Control  emm_v5.o
    0x080002b2   0x080002b2   0x00000070   Code   RO          158    i.Emm_V5_Pos_Control_USART  emm_v5.o
    0x08000322   0x08000322   0x0000002e   Code   RO          163    i.Emm_V5_Reset_CurPos_To_Zero  emm_v5.o
    0x08000350   0x08000350   0x00000032   Code   RO          164    i.Emm_V5_Reset_CurPos_To_Zero_USART  emm_v5.o
    0x08000382   0x08000382   0x00000024   Code   RO          165    i.Emm_V5_SendCmd_USART  emm_v5.o
    0x080003a6   0x080003a6   0x0000002e   Code   RO          168    i.Emm_V5_Synchronous_motion  emm_v5.o
    0x080003d4   0x080003d4   0x00000116   Code   RO          521    i.GPIO_Init         stm32f10x_gpio.o
    0x080004ea   0x080004ea   0x00000002   PAD
    0x080004ec   0x080004ec   0x00000090   Code   RO          523    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x0800057c   0x0800057c   0x00000004   Code   RO           84    i.HardFault_Handler  stm32f10x_it.o
    0x08000580   0x08000580   0x00000004   Code   RO           85    i.MemManage_Handler  stm32f10x_it.o
    0x08000584   0x08000584   0x00000002   Code   RO           86    i.NMI_Handler       stm32f10x_it.o
    0x08000586   0x08000586   0x00000002   PAD
    0x08000588   0x08000588   0x00000070   Code   RO         1009    i.NVIC_Init         misc.o
    0x080005f8   0x080005f8   0x00000014   Code   RO         1010    i.NVIC_PriorityGroupConfig  misc.o
    0x0800060c   0x0800060c   0x00000002   Code   RO           87    i.PendSV_Handler    stm32f10x_it.o
    0x0800060e   0x0800060e   0x00000002   PAD
    0x08000610   0x08000610   0x00000020   Code   RO          631    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000630   0x08000630   0x00000020   Code   RO          633    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000650   0x08000650   0x000000d4   Code   RO          641    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000724   0x08000724   0x00000002   Code   RO           88    i.SVC_Handler       stm32f10x_it.o
    0x08000726   0x08000726   0x00000008   Code   RO          421    i.SetSysClock       system_stm32f10x.o
    0x0800072e   0x0800072e   0x00000002   PAD
    0x08000730   0x08000730   0x000000e0   Code   RO          422    i.SetSysClockTo72   system_stm32f10x.o
    0x08000810   0x08000810   0x00000002   Code   RO           89    i.SysTick_Handler   stm32f10x_it.o
    0x08000812   0x08000812   0x00000002   PAD
    0x08000814   0x08000814   0x00000060   Code   RO          424    i.SystemInit        system_stm32f10x.o
    0x08000874   0x08000874   0x000000dc   Code   RO          312    i.USART1_IRQHandler  usart.o
    0x08000950   0x08000950   0x00000094   Code   RO          313    i.USART2_IRQHandler  usart.o
    0x080009e4   0x080009e4   0x0000001e   Code   RO          830    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08000a02   0x08000a02   0x00000018   Code   RO          833    i.USART_Cmd         stm32f10x_usart.o
    0x08000a1a   0x08000a1a   0x00000054   Code   RO          837    i.USART_GetITStatus  stm32f10x_usart.o
    0x08000a6e   0x08000a6e   0x0000004a   Code   RO          839    i.USART_ITConfig    stm32f10x_usart.o
    0x08000ab8   0x08000ab8   0x000000d8   Code   RO          840    i.USART_Init        stm32f10x_usart.o
    0x08000b90   0x08000b90   0x00000004   Code   RO           90    i.UsageFault_Handler  stm32f10x_it.o
    0x08000b94   0x08000b94   0x0000000e   Code   RO         1068    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08000ba2   0x08000ba2   0x00000002   Code   RO         1069    i.__scatterload_null  mc_w.l(handlers.o)
    0x08000ba4   0x08000ba4   0x0000000e   Code   RO         1070    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08000bb2   0x08000bb2   0x00000002   PAD
    0x08000bb4   0x08000bb4   0x00000024   Code   RO          373    i.board_init        board.o
    0x08000bd8   0x08000bd8   0x00000028   Code   RO          374    i.clock_init        board.o
    0x08000c00   0x08000c00   0x00000060   Code   RO          498    i.delay_ms          delay.o
    0x08000c60   0x08000c60   0x00000064   Code   RO            1    i.dual_motor_return_to_zero  main.o
    0x08000cc4   0x08000cc4   0x00000026   Code   RO          455    i.fifo_deQueue      fifo.o
    0x08000cea   0x08000cea   0x00000022   Code   RO          456    i.fifo_enQueue      fifo.o
    0x08000d0c   0x08000d0c   0x0000000c   Code   RO          457    i.fifo_initQueue    fifo.o
    0x08000d18   0x08000d18   0x0000002e   Code   RO          459    i.fifo_queueLength  fifo.o
    0x08000d46   0x08000d46   0x00000002   PAD
    0x08000d48   0x08000d48   0x00000128   Code   RO            2    i.main              main.o
    0x08000e70   0x08000e70   0x0000003a   Code   RO          375    i.nvic_init         board.o
    0x08000eaa   0x08000eaa   0x00000002   PAD
    0x08000eac   0x08000eac   0x0000003c   Code   RO          314    i.usart1_SendByte   usart.o
    0x08000ee8   0x08000ee8   0x0000002c   Code   RO          315    i.usart1_SendCmd    usart.o
    0x08000f14   0x08000f14   0x0000003c   Code   RO          316    i.usart2_SendByte   usart.o
    0x08000f50   0x08000f50   0x0000002c   Code   RO          317    i.usart2_SendCmd    usart.o
    0x08000f7c   0x08000f7c   0x0000010c   Code   RO          376    i.usart_init        board.o
    0x08001088   0x08001088   0x00000020   Data   RO         1066    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080010a8, Size: 0x00000bb8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080010a8   0x00000006   Data   RW          321    .data               usart.o
    0x20000006   0x080010ae   0x00000002   PAD
    0x20000008   0x080010b0   0x00000014   Data   RW          425    .data               system_stm32f10x.o
    0x2000001c   0x080010c4   0x00000014   Data   RW          661    .data               stm32f10x_rcc.o
    0x20000030        -       0x00000180   Zero   RW          320    .bss                usart.o
    0x200001b0        -       0x00000204   Zero   RW          460    .bss                fifo.o
    0x200003b4   0x080010d8   0x00000004   PAD
    0x200003b8        -       0x00000800   Zero   RW          414    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       402         26          0          0          0       2139   board.o
         0          0          0          0          0         32   core_cm3.o
        96          6          0          0          0        991   delay.o
       578          0          0          0          0       6712   emm_v5.o
       130          0          0          0        516       3252   fifo.o
       396          4          0          0          0     232459   main.o
       132         22          0          0          0       1867   misc.o
        36          8        304          0       2048        808   startup_stm32f10x_hd.o
       422          6          0          0          0       3126   stm32f10x_gpio.o
        26          0          0          0          0       4078   stm32f10x_it.o
       276         32          0         20          0       4870   stm32f10x_rcc.o
       428          6          0          0          0       5604   stm32f10x_usart.o
       328         28          0         20          0       2425   system_stm32f10x.o
       576         64          0          6        384       4428   usart.o

    ----------------------------------------------------------------------
      3840        <USER>        <GROUP>         48       2952     272791   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          2          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o

    ----------------------------------------------------------------------
        88         <USER>          <GROUP>          0          0         68   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        86         16          0          0          0         68   mc_w.l

    ----------------------------------------------------------------------
        88         <USER>          <GROUP>          0          0         68   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3928        218        336         48       2952     270619   Grand Totals
      3928        218        336         48       2952     270619   ELF Image Totals
      3928        218        336         48          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 4264 (   4.16kB)
    Total RW  Size (RW Data + ZI Data)              3000 (   2.93kB)
    Total ROM Size (Code + RO Data + RW Data)       4312 (   4.21kB)

==============================================================================

