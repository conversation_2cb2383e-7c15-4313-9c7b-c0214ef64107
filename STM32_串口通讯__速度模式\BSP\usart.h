#ifndef __USART_H
#define __USART_H

#include "board.h"
#include "fifo.h"

/**********************************************************
***	Emm_V5.0�����ջ���������
***	��д���ߣ�ZHANGDATOU
***	����֧�֣��Ŵ�ͷ�ջ��ŷ�
***	�Ա����̣�https://zhangdatou.taobao.com
***	CSDN���ͣ�http s://blog.csdn.net/zhangdatou666
***	qq����Ⱥ��262438510
**********************************************************/

// USART1 ����
extern __IO bool rxFrameFlag1;
extern __IO uint8_t rxCmd1[FIFO_SIZE];
extern __IO uint8_t rxCount1;

// USART2 ����
extern __IO bool rxFrameFlag2;
extern __IO uint8_t rxCmd2[FIFO_SIZE];
extern __IO uint8_t rxCount2;

// ����������
extern __IO bool rxFrameFlag; // ����USART1����
extern __IO uint8_t rxCmd[FIFO_SIZE]; // ����USART1����
extern __IO uint8_t rxCount; // ����USART1����

void usart1_SendCmd(__IO uint8_t *cmd, uint8_t len);
void usart1_SendByte(uint16_t data);
void usart2_SendCmd(__IO uint8_t *cmd, uint8_t len);
void usart2_SendByte(uint16_t data);

// ����������
void usart_SendCmd(__IO uint8_t *cmd, uint8_t len); // ����USART1
void usart_SendByte(uint16_t data); // ����USART1

#endif
