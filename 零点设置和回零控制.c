#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	零点设置和回零控制程序
***	功能：
***	1. 上电时将当前位置设置为零点
***	2. 提供回零命令功能
***	3. 支持双电机独立零点控制
**********************************************************/

// 回零参数定义
#define RETURN_SPEED        800     // 回零速度 (RPM)
#define RETURN_ACCELERATION 10      // 回零加速度
#define RETURN_TIMEOUT      30000   // 回零超时时间 (ms)

// 全局变量：零点设置标志
static bool motor1_zero_set = false;
static bool motor2_zero_set = false;

/**
 * @brief 初始化时设置零点位置
 */
void init_set_zero_position(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);  // 等待驱动器初始化
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);           // 使能电机1
    Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机2
    delay_ms(100);
    
    // 方法1：复位当前位置计数器为零（推荐）
    Emm_V5_Reset_CurPos_To_Zero(1);              // 电机1位置复位为0
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);     // 电机2位置复位为0
    delay_ms(100);
    
    // 方法2：设置当前位置为原点（可选，更正式的零点设置）
    Emm_V5_Origin_Set_O(1, true);                // 电机1设置原点并保存
    Emm_V5_Origin_Set_O_USART(2, 1, true);       // 电机2设置原点并保存
    delay_ms(100);
    
    // 设置零点标志
    motor1_zero_set = true;
    motor2_zero_set = true;
    
    // 可选：配置回零参数
    configure_return_to_zero_params();
}

/**
 * @brief 配置回零参数
 */
void configure_return_to_zero_params(void)
{
    // 配置电机1回零参数
    // 参数说明：地址, 保存标志, 回零模式, 回零方向, 回零速度, 超时时间, 慢速, 电流, 时间, 电位器标志
    Emm_V5_Origin_Modify_Params(1, true, 0, 0, RETURN_SPEED, RETURN_TIMEOUT, 
                                200, 100, 1000, false);
    delay_ms(100);
    
    // 配置电机2回零参数
    Emm_V5_Origin_Modify_Params_USART(2, 1, true, 0, 0, RETURN_SPEED, RETURN_TIMEOUT, 
                                      200, 100, 1000, false);
    delay_ms(100);
}

/**
 * @brief 电机1回零命令
 */
void motor1_return_to_zero(void)
{
    if (!motor1_zero_set) {
        // 如果零点未设置，先设置零点
        init_set_zero_position();
        return;
    }
    
    // 使能电机1
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 方法1：使用绝对位置控制回零（推荐）
    Emm_V5_Pos_Control(1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
    
    // 方法2：使用原点回归功能（可选）
    // Emm_V5_Origin_Trigger_Return(1, 0, false);  // 触发回零
    // delay_ms(5000);  // 等待回零完成
}

/**
 * @brief 电机2回零命令
 */
void motor2_return_to_zero(void)
{
    if (!motor2_zero_set) {
        // 如果零点未设置，先设置零点
        init_set_zero_position();
        return;
    }
    
    // 使能电机2
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 方法1：使用绝对位置控制回零（推荐）
    Emm_V5_Pos_Control_USART(2, 1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
    
    // 方法2：使用原点回归功能（可选）
    // Emm_V5_Origin_Trigger_Return_USART(2, 1, 0, false);  // 触发回零
    // delay_ms(5000);  // 等待回零完成
}

/**
 * @brief 双电机同步回零
 */
void dual_motor_return_to_zero(void)
{
    if (!motor1_zero_set || !motor2_zero_set) {
        // 如果零点未设置，先设置零点
        init_set_zero_position();
        return;
    }
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 同步回零：设置同步标志为true
    Emm_V5_Pos_Control(1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, true);
    Emm_V5_Pos_Control_USART(2, 1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, true);
    
    delay_ms(100);  // 确保命令发送完成
    
    // 触发同步回零
    Emm_V5_Synchronous_motion(1);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 重新设置零点位置
 */
void reset_zero_position(void)
{
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 复位当前位置为零点
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    delay_ms(100);
    
    // 设置当前位置为原点
    Emm_V5_Origin_Set_O(1, true);
    Emm_V5_Origin_Set_O_USART(2, 1, true);
    delay_ms(100);
    
    // 更新零点设置标志
    motor1_zero_set = true;
    motor2_zero_set = true;
}

/**
 * @brief 测试程序：演示零点设置和回零功能
 */
void zero_point_test_demo(void)
{
    // 1. 初始化并设置零点
    init_set_zero_position();
    delay_ms(1000);
    
    // 2. 移动电机到不同位置
    // 电机1移动到180°位置
    Emm_V5_Pos_Control(1, 0, 1000, 15, 1600, false, false);
    delay_ms(1200);
    
    // 电机2移动到270°位置
    Emm_V5_Pos_Control_USART(2, 1, 0, 1000, 15, 2400, false, false);
    delay_ms(1500);
    
    delay_ms(2000);  // 停顿2秒
    
    // 3. 测试单独回零
    motor1_return_to_zero();  // 电机1回零
    delay_ms(1000);
    
    motor2_return_to_zero();  // 电机2回零
    delay_ms(1000);
    
    // 4. 再次移动到不同位置
    Emm_V5_Pos_Control(1, 1, 800, 10, 800, false, false);   // 电机1逆时针90°
    Emm_V5_Pos_Control_USART(2, 1, 1, 800, 10, 1200, false, false); // 电机2逆时针135°
    delay_ms(2000);
    
    delay_ms(2000);  // 停顿2秒
    
    // 5. 测试同步回零
    dual_motor_return_to_zero();
    delay_ms(1000);
}

/**
 * @brief 命令处理函数：根据接收到的命令执行相应操作
 */
void process_zero_commands(void)
{
    // 检查串口接收到的命令
    if(rxFrameFlag1) {  // USART1接收到命令
        rxFrameFlag1 = false;
        
        // 解析命令（简单示例）
        if(rxCmd1[0] == 0x01 && rxCmd1[1] == 0x00) {  // 电机1回零命令
            motor1_return_to_zero();
        }
        else if(rxCmd1[0] == 0x02 && rxCmd1[1] == 0x00) {  // 电机2回零命令
            motor2_return_to_zero();
        }
        else if(rxCmd1[0] == 0x03 && rxCmd1[1] == 0x00) {  // 双电机回零命令
            dual_motor_return_to_zero();
        }
        else if(rxCmd1[0] == 0x04 && rxCmd1[1] == 0x00) {  // 重新设置零点命令
            reset_zero_position();
        }
    }
    
    if(rxFrameFlag2) {  // USART2接收到命令
        rxFrameFlag2 = false;
        // 可以添加USART2的命令处理
    }
}

/**
 * @brief 主函数
 */
int main(void)
{
    // 初始化并设置零点位置
    init_set_zero_position();
    
    // 运行测试演示
    zero_point_test_demo();
    
    // 主循环：处理回零命令
    while(1)
    {
        // 处理零点相关命令
        process_zero_commands();
        
        delay_ms(100);
    }
}

/**********************************************************
***	零点设置和回零功能说明
***
***	1. 零点设置方法：
***	   - Emm_V5_Reset_CurPos_To_Zero(): 复位位置计数器
***	   - Emm_V5_Origin_Set_O(): 设置当前位置为原点
***
***	2. 回零方法：
***	   - 绝对位置控制: Emm_V5_Pos_Control(addr, dir, vel, acc, 0, true, false)
***	   - 原点回归功能: Emm_V5_Origin_Trigger_Return()
***
***	3. 命令格式（示例）：
***	   - 0x01 0x00: 电机1回零
***	   - 0x02 0x00: 电机2回零  
***	   - 0x03 0x00: 双电机同步回零
***	   - 0x04 0x00: 重新设置零点
***
***	4. 使用建议：
***	   - 上电时先调用init_set_zero_position()
***	   - 推荐使用绝对位置控制方式回零
***	   - 回零前确保电机已使能
***	   - 根据实际情况调整回零速度和加速度
**********************************************************/
