# 第二个电机抖动不转问题解决指南

## 🔍 问题分析

### 原始问题
- **现象**：第二个电机抖动很大但几乎不转
- **根本原因**：缺少电机使能控制步骤

### 第一个电机的正确启动逻辑
```c
// 第一个电机的完整启动流程
board_init();                              // 1. 硬件初始化
delay_ms(2000);                           // 2. 等待驱动器初始化
Emm_V5_En_Control(1, true, false);        // 3. 【关键】使能电机
delay_ms(100);                            // 4. 等待使能完成
Emm_V5_Vel_Control(1, 0, 1000, 10, false); // 5. 速度控制
```

## ✅ 解决方案

### 修复后的第二个电机启动逻辑
```c
// 第二个电机的正确启动流程
board_init();                                    // 1. 硬件初始化
delay_ms(2000);                                 // 2. 等待驱动器初始化
Emm_V5_En_Control_USART(2, 1, true, false);     // 3. 【关键】使能第二个电机
delay_ms(100);                                  // 4. 等待使能完成
Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false); // 5. 速度控制
```

### 关键修复点

#### 1. 添加电机使能控制
```c
// ❌ 错误：直接进行速度控制
Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);

// ✅ 正确：先使能，再控制
Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机
delay_ms(100);                               // 等待使能完成
Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false); // 速度控制
```

#### 2. 修复参数顺序
```c
// ❌ 错误的参数顺序
Emm_V5_Vel_Control_USART(2, 0, 1, 800, 0, 0);

// ✅ 正确的参数顺序
Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
//                      串口 地址 方向 速度 加速度 同步
```

#### 3. 设置合适的加速度
```c
// ❌ 错误：加速度为0可能导致启动不稳定
Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 0, false);

// ✅ 正确：设置合适的加速度值
Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);
```

## 📋 完整的函数参数说明

### Emm_V5_Vel_Control_USART 参数详解
```c
void Emm_V5_Vel_Control_USART(uint8_t usart_num, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF);
```

| 参数 | 类型 | 说明 | 第二个电机的值 |
|------|------|------|----------------|
| usart_num | uint8_t | 串口号 | 固定为 `2` |
| addr | uint8_t | 电机地址 | 通常为 `1` |
| dir | uint8_t | 方向 | `0`=顺时针, `1`=逆时针 |
| vel | uint16_t | 速度(RPM) | 范围 0-5000 |
| acc | uint8_t | 加速度 | 建议 5-20 |
| snF | bool | 同步标志 | `false`=立即执行 |

### Emm_V5_En_Control_USART 参数详解
```c
void Emm_V5_En_Control_USART(uint8_t usart_num, uint8_t addr, bool state, bool snF);
```

| 参数 | 类型 | 说明 | 第二个电机的值 |
|------|------|------|----------------|
| usart_num | uint8_t | 串口号 | 固定为 `2` |
| addr | uint8_t | 电机地址 | 通常为 `1` |
| state | bool | 使能状态 | `true`=使能, `false`=禁用 |
| snF | bool | 同步标志 | 通常为 `false` |

## 🔧 测试程序

### 基础测试程序
```c
void motor2_test(void)
{
    // 1. 初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能第二个电机（关键步骤）
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 低速测试
    Emm_V5_Vel_Control_USART(2, 1, 0, 300, 5, false);
    delay_ms(3000);
    
    // 4. 停止
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 5. 禁用电机
    Emm_V5_En_Control_USART(2, 1, false, false);
}
```

## 🚨 故障排除

### 如果电机仍然抖动不转

#### 硬件检查
1. **连接检查**：
   - PA2 (USART2_TX) -> 驱动器RX
   - PA3 (USART2_RX) -> 驱动器TX
   - GND -> GND（必须共地）
   - 5V -> VCC

2. **电源检查**：
   - 确保驱动器有足够的电源供应
   - 检查电源电压是否稳定

3. **驱动器设置**：
   - 检查驱动器的细分设置
   - 检查电流设置是否合适
   - 确认驱动器地址设置

#### 软件检查
1. **参数检查**：
   - 确认串口号为2
   - 确认电机地址正确
   - 尝试不同的速度和加速度

2. **初始化检查**：
   - 确保board_init()正确调用
   - 确保USART2正确初始化
   - 检查中断是否正常

#### 调试方法
1. **使用示波器**：
   - 检查PA2的TX信号是否正常
   - 确认波特率为115200

2. **LED指示**：
   - 观察驱动器的LED状态
   - 正常情况下应有通信指示

3. **逐步测试**：
   - 先测试使能功能
   - 再测试低速运行
   - 最后测试高速运行

## 📝 推荐的测试步骤

1. **第一步**：运行基础测试程序验证基本功能
2. **第二步**：如果正常，尝试双电机对比测试
3. **第三步**：测试不同速度和方向
4. **第四步**：测试连续运行稳定性

## 💡 优化建议

1. **启动参数**：
   - 初始速度建议300-500RPM
   - 加速度建议5-15
   - 避免使用0加速度

2. **运行参数**：
   - 常用速度范围：300-2000RPM
   - 高速运行时增加加速度值
   - 方向切换时先停止再启动

3. **稳定性**：
   - 使能后延时100ms再控制
   - 停止后延时500ms再下一步操作
   - 程序结束时禁用电机

通过以上修复和测试，第二个电机应该能够正常运行，不再出现抖动不转的问题。
