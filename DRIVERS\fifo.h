#ifndef __FIFO_H
#define __FIFO_H

#include "stm32f10x.h"

/**********************************************************
***	Emm_V5.0�����ջ���������
***	��д���ߣ�ZHANGDATOU
***	����֧�֣��Ŵ�ͷ�ջ��ŷ�
***	�Ա����̣�https://zhangdatou.taobao.com
***	CSDN���ͣ�http s://blog.csdn.net/zhangdatou666
***	qq����Ⱥ��262438510
**********************************************************/

#define 	FIFO_SIZE   128
typedef struct {
	uint16_t buffer[FIFO_SIZE];
	__IO uint8_t ptrWrite;
	__IO uint8_t ptrRead;
}FIFO_t;

extern __IO FIFO_t rxFIFO1; // USART1 FIFO
extern __IO FIFO_t rxFIFO2; // USART2 FIFO

void fifo_initQueue(FIFO_t *fifo);
void fifo_enQueue(FIFO_t *fifo, uint16_t data);
uint16_t fifo_deQueue(FIFO_t *fifo);
bool fifo_isEmpty(FIFO_t *fifo);
uint16_t fifo_queueLength(FIFO_t *fifo);

#endif
