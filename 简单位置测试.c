#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	简单位置模式测试程序
***	目标：电机1顺时针，电机2逆时针，各180°每秒
***	参数：1600脉冲 = 180°，1600RPM = 180°每秒
**********************************************************/

int main(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);  // 等待驱动器初始化
    
    // 2. 使能两个电机（关键步骤）
    Emm_V5_En_Control(1, true, false);           // 使能电机1
    Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机2
    delay_ms(100);   // 等待使能完成
    
    // 3. 第一次位置运动测试
    // 电机1：顺时针180°，1600RPM（180°每秒），加速度15，1600脉冲
    Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);
    
    // 电机2：逆时针180°，1600RPM（180°每秒），加速度15，1600脉冲
    Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);
    
    // 4. 等待第一次运动完成
    delay_ms(1200);  // 180°理论需要1秒，加上加减速时间
    
    // 5. 主循环 - 连续180°旋转测试
    while(1)
    {
        // 间隔2秒
        delay_ms(2000);
        
        // 继续180°旋转（相同方向）
        Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);        // 电机1顺时针
        Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false); // 电机2逆时针
        
        // 等待运动完成
        delay_ms(1200);
    }
}

/**********************************************************
***	位置模式参数说明：
***
***	Emm_V5_Pos_Control(地址, 方向, 速度, 加速度, 脉冲数, 相对位置, 同步标志)
***	Emm_V5_Pos_Control_USART(串口号, 地址, 方向, 速度, 加速度, 脉冲数, 相对位置, 同步标志)
***
***	参数值说明：
***	- 地址: 1 (电机地址)
***	- 方向: 0=顺时针, 1=逆时针
***	- 速度: 1600RPM (对应180°每秒)
***	- 加速度: 15 (适中的加速度)
***	- 脉冲数: 1600 (对应180°)
***	- 相对位置: false (从当前位置开始计算)
***	- 同步标志: false (立即执行)
***
***	关键计算：
***	- 驱动器设置: 3200脉冲/圈 (16细分)
***	- 180° = 3200脉冲 ÷ 2 = 1600脉冲
***	- 180°每秒 = 1600脉冲每秒 × 60秒每分钟 ÷ 3200脉冲每圈 = 1600RPM
***
***	时间计算：
***	- 理论运动时间 = 1600脉冲 ÷ (1600RPM × 3200脉冲每圈 ÷ 60秒) = 1秒
***	- 实际等待时间 = 1秒 + 0.2秒(加减速) = 1.2秒
**********************************************************/
