#ifndef __MOTOR_RETURN_ZERO_H
#define __MOTOR_RETURN_ZERO_H

#include "stm32f10x.h"

/**********************************************************
***	电机1和电机2回零函数库头文件（简化版）
***	只保留有效的回零函数，解决电机2同步问题
**********************************************************/

// 回零参数配置宏定义
#define DEFAULT_RETURN_SPEED        800     // 默认回零速度 (RPM)
#define DEFAULT_RETURN_ACCELERATION 10      // 默认回零加速度

/**********************************************************
***	核心回零函数声明（简化版）
**********************************************************/

/**
 * @brief 电机1回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Return_To_Zero(void);

/**
 * @brief 电机2回零函数（解决地址问题）
 * @param 无
 * @retval 无
 * @note 自动尝试多个地址，解决电机2地址设置问题
 */
void Motor2_Return_To_Zero(void);

/**
 * @brief 双电机同步回零函数（解决电机2同步问题）
 * @param 无
 * @retval 无
 * @note 解决电机2在同步回零时的地址问题
 */
void Dual_Motor_Sync_Return_To_Zero(void);

/**
 * @brief 回零函数使用示例
 * @param 无
 * @retval 无
 */
void Return_To_Zero_Examples(void);

/**********************************************************
***	便捷宏定义（简化版）
**********************************************************/

// 快速调用宏
#define MOTOR1_RETURN_ZERO()        Motor1_Return_To_Zero()
#define MOTOR2_RETURN_ZERO()        Motor2_Return_To_Zero()
#define DUAL_MOTOR_RETURN_ZERO()    Dual_Motor_Sync_Return_To_Zero()

#endif /* __MOTOR_RETURN_ZERO_H */

/**********************************************************
***	简化版使用说明：
***
***	1. 包含头文件：#include "电机回零函数库.h"
***
***	2. 基本使用：
***	   Motor1_Return_To_Zero();     // 电机1回零
***	   Motor2_Return_To_Zero();     // 电机2回零（自动解决地址问题）
***	   Dual_Motor_Sync_Return_To_Zero(); // 双电机同步回零
***
***	3. 便捷宏使用：
***	   MOTOR1_RETURN_ZERO();        // 电机1回零
***	   MOTOR2_RETURN_ZERO();        // 电机2回零
***	   DUAL_MOTOR_RETURN_ZERO();    // 双电机同步回零
**********************************************************/
