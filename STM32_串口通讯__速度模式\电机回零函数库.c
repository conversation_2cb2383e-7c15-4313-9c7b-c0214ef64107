#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	电机1和电机2回零函数库
***	提供多种回零方式和参数配置
***	支持单独回零、同步回零、带参数回零等
**********************************************************/

// 回零参数配置
#define DEFAULT_RETURN_SPEED        800     // 默认回零速度 (RPM)
#define DEFAULT_RETURN_ACCELERATION 10      // 默认回零加速度
#define SLOW_RETURN_SPEED          400     // 慢速回零速度 (RPM)
#define FAST_RETURN_SPEED          1200    // 快速回零速度 (RPM)
#define HIGH_ACCELERATION          20      // 高加速度
#define LOW_ACCELERATION           5       // 低加速度

/**********************************************************
***	电机1回零函数系列
**********************************************************/

/**
 * @brief 电机1基础回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Return_To_Zero(void)
{
    // 确保电机1使能
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 使用绝对位置控制回到零点
    Emm_V5_Pos_Control(1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 电机1带参数回零函数
 * @param speed 回零速度 (RPM)
 * @param acceleration 回零加速度
 * @retval 无
 */
void Motor1_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration)
{
    // 参数范围检查
    if(speed > 3000) speed = 3000;
    if(speed < 100) speed = 100;
    if(acceleration > 50) acceleration = 50;
    if(acceleration < 3) acceleration = 3;
    
    // 确保电机1使能
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 使用指定参数回零
    Emm_V5_Pos_Control(1, 0, speed, acceleration, 0, true, false);
    
    // 根据速度计算等待时间（估算）
    uint16_t wait_time = (uint16_t)(2000.0 * DEFAULT_RETURN_SPEED / speed) + 500;
    delay_ms(wait_time);
}

/**
 * @brief 电机1快速回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Fast_Return_To_Zero(void)
{
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    Emm_V5_Pos_Control(1, 0, FAST_RETURN_SPEED, HIGH_ACCELERATION, 0, true, false);
    delay_ms(1500);  // 快速回零，等待时间较短
}

/**
 * @brief 电机1慢速精确回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Slow_Return_To_Zero(void)
{
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    Emm_V5_Pos_Control(1, 0, SLOW_RETURN_SPEED, LOW_ACCELERATION, 0, true, false);
    delay_ms(3000);  // 慢速回零，等待时间较长
}

/**
 * @brief 电机1同步回零函数（设置同步标志）
 * @param 无
 * @retval 无
 */
void Motor1_Sync_Return_To_Zero(void)
{
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 设置同步标志为true，需要外部触发同步运动
    Emm_V5_Pos_Control(1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, true);
}

/**********************************************************
***	电机2回零函数系列
**********************************************************/

/**
 * @brief 电机2基础回零函数
 * @param 无
 * @retval 无
 */
void Motor2_Return_To_Zero(void)
{
    // 确保电机2使能
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 使用绝对位置控制回到零点
    Emm_V5_Pos_Control_USART(2, 1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 电机2带参数回零函数
 * @param speed 回零速度 (RPM)
 * @param acceleration 回零加速度
 * @retval 无
 */
void Motor2_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration)
{
    // 参数范围检查
    if(speed > 3000) speed = 3000;
    if(speed < 100) speed = 100;
    if(acceleration > 50) acceleration = 50;
    if(acceleration < 3) acceleration = 3;
    
    // 确保电机2使能
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 使用指定参数回零
    Emm_V5_Pos_Control_USART(2, 1, 0, speed, acceleration, 0, true, false);
    
    // 根据速度计算等待时间（估算）
    uint16_t wait_time = (uint16_t)(2000.0 * DEFAULT_RETURN_SPEED / speed) + 500;
    delay_ms(wait_time);
}

/**
 * @brief 电机2快速回零函数
 * @param 无
 * @retval 无
 */
void Motor2_Fast_Return_To_Zero(void)
{
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    Emm_V5_Pos_Control_USART(2, 1, 0, FAST_RETURN_SPEED, HIGH_ACCELERATION, 0, true, false);
    delay_ms(1500);  // 快速回零，等待时间较短
}

/**
 * @brief 电机2慢速精确回零函数
 * @param 无
 * @retval 无
 */
void Motor2_Slow_Return_To_Zero(void)
{
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    Emm_V5_Pos_Control_USART(2, 1, 0, SLOW_RETURN_SPEED, LOW_ACCELERATION, 0, true, false);
    delay_ms(3000);  // 慢速回零，等待时间较长
}

/**
 * @brief 电机2同步回零函数（设置同步标志）
 * @param 无
 * @retval 无
 */
void Motor2_Sync_Return_To_Zero(void)
{
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置同步标志为true，需要外部触发同步运动
    Emm_V5_Pos_Control_USART(2, 1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, true);
}

/**
 * @brief 电机2多地址尝试回零函数（解决地址问题）
 * @param 无
 * @retval 无
 */
void Motor2_Multi_Address_Return_To_Zero(void)
{
    // 尝试不同地址的使能和回零
    uint8_t addresses[] = {1, 2, 3, 4, 5};
    
    for(int i = 0; i < 5; i++)
    {
        uint8_t addr = addresses[i];
        
        // 使能电机
        Emm_V5_En_Control_USART(2, addr, true, false);
        delay_ms(100);
        
        // 回零
        Emm_V5_Pos_Control_USART(2, addr, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, false);
        delay_ms(100);  // 短暂延时，不等待完成
    }
    
    // 等待回零完成
    delay_ms(2000);
}

/**********************************************************
***	双电机回零函数系列
**********************************************************/

/**
 * @brief 双电机独立回零函数
 * @param 无
 * @retval 无
 */
void Dual_Motor_Independent_Return_To_Zero(void)
{
    // 先回零电机1
    Motor1_Return_To_Zero();
    delay_ms(500);
    
    // 再回零电机2
    Motor2_Return_To_Zero();
    delay_ms(500);
}

/**
 * @brief 双电机同步回零函数
 * @param 无
 * @retval 无
 */
void Dual_Motor_Sync_Return_To_Zero(void)
{
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置同步回零参数
    Emm_V5_Pos_Control(1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, true);
    Emm_V5_Pos_Control_USART(2, 1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, true);
    delay_ms(100);
    
    // 触发同步运动
    Emm_V5_Synchronous_motion(1);
    delay_ms(2000);  // 等待同步回零完成
}

/**
 * @brief 双电机带参数同步回零函数
 * @param speed 回零速度 (RPM)
 * @param acceleration 回零加速度
 * @retval 无
 */
void Dual_Motor_Sync_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration)
{
    // 参数检查
    if(speed > 3000) speed = 3000;
    if(speed < 100) speed = 100;
    if(acceleration > 50) acceleration = 50;
    if(acceleration < 3) acceleration = 3;
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置同步回零参数
    Emm_V5_Pos_Control(1, 0, speed, acceleration, 0, true, true);
    Emm_V5_Pos_Control_USART(2, 1, 0, speed, acceleration, 0, true, true);
    delay_ms(100);
    
    // 触发同步运动
    Emm_V5_Synchronous_motion(1);
    
    // 根据速度计算等待时间
    uint16_t wait_time = (uint16_t)(2000.0 * DEFAULT_RETURN_SPEED / speed) + 500;
    delay_ms(wait_time);
}

/**********************************************************
***	高级回零函数
**********************************************************/

/**
 * @brief 智能回零函数（自动选择最优参数）
 * @param motor_num 电机编号 (1或2)
 * @param current_position 当前位置（脉冲数，估算值）
 * @retval 无
 */
void Smart_Return_To_Zero(uint8_t motor_num, uint32_t current_position)
{
    uint16_t speed;
    uint8_t acceleration;
    uint16_t wait_time;
    
    // 根据距离选择参数
    if(current_position > 6400) {  // 距离较远（>2圈）
        speed = FAST_RETURN_SPEED;
        acceleration = HIGH_ACCELERATION;
        wait_time = 2500;
    } else if(current_position > 1600) {  // 中等距离（>0.5圈）
        speed = DEFAULT_RETURN_SPEED;
        acceleration = DEFAULT_RETURN_ACCELERATION;
        wait_time = 2000;
    } else {  // 距离较近（<0.5圈）
        speed = SLOW_RETURN_SPEED;
        acceleration = LOW_ACCELERATION;
        wait_time = 1500;
    }
    
    // 执行回零
    if(motor_num == 1) {
        Motor1_Return_To_Zero_WithParams(speed, acceleration);
    } else if(motor_num == 2) {
        Motor2_Return_To_Zero_WithParams(speed, acceleration);
    }
}

/**
 * @brief 安全回零函数（带错误检查）
 * @param motor_num 电机编号 (1或2)
 * @retval 回零是否成功 (true=成功, false=失败)
 */
bool Safe_Return_To_Zero(uint8_t motor_num)
{
    // 尝试回零
    if(motor_num == 1) {
        Motor1_Return_To_Zero();
    } else if(motor_num == 2) {
        Motor2_Return_To_Zero();
    } else {
        return false;  // 无效的电机编号
    }
    
    // 简单的成功检查（实际应用中可以读取位置反馈）
    delay_ms(100);
    
    // 这里可以添加位置读取和验证代码
    // 例如：读取当前位置，检查是否接近零点
    
    return true;  // 假设成功
}

/**********************************************************
***	使用示例函数
**********************************************************/

/**
 * @brief 回零函数使用示例
 * @param 无
 * @retval 无
 */
void Return_To_Zero_Examples(void)
{
    // 示例1：基础回零
    Motor1_Return_To_Zero();
    Motor2_Return_To_Zero();
    
    delay_ms(1000);
    
    // 示例2：快速回零
    Motor1_Fast_Return_To_Zero();
    Motor2_Fast_Return_To_Zero();
    
    delay_ms(1000);
    
    // 示例3：慢速精确回零
    Motor1_Slow_Return_To_Zero();
    Motor2_Slow_Return_To_Zero();
    
    delay_ms(1000);
    
    // 示例4：带参数回零
    Motor1_Return_To_Zero_WithParams(600, 8);
    Motor2_Return_To_Zero_WithParams(600, 8);
    
    delay_ms(1000);
    
    // 示例5：同步回零
    Dual_Motor_Sync_Return_To_Zero();
    
    delay_ms(1000);
    
    // 示例6：智能回零
    Smart_Return_To_Zero(1, 3200);  // 电机1，当前位置约1圈
    Smart_Return_To_Zero(2, 800);   // 电机2，当前位置约90°
}

/**********************************************************
***	函数说明：
***
***	电机1回零函数：
***	- Motor1_Return_To_Zero()                    // 基础回零
***	- Motor1_Return_To_Zero_WithParams()         // 带参数回零
***	- Motor1_Fast_Return_To_Zero()               // 快速回零
***	- Motor1_Slow_Return_To_Zero()               // 慢速回零
***	- Motor1_Sync_Return_To_Zero()               // 同步回零
***
***	电机2回零函数：
***	- Motor2_Return_To_Zero()                    // 基础回零
***	- Motor2_Return_To_Zero_WithParams()         // 带参数回零
***	- Motor2_Fast_Return_To_Zero()               // 快速回零
***	- Motor2_Slow_Return_To_Zero()               // 慢速回零
***	- Motor2_Sync_Return_To_Zero()               // 同步回零
***	- Motor2_Multi_Address_Return_To_Zero()      // 多地址尝试回零
***
***	双电机回零函数：
***	- Dual_Motor_Independent_Return_To_Zero()    // 独立回零
***	- Dual_Motor_Sync_Return_To_Zero()           // 同步回零
***	- Dual_Motor_Sync_Return_To_Zero_WithParams() // 带参数同步回零
***
***	高级回零函数：
***	- Smart_Return_To_Zero()                     // 智能回零
***	- Safe_Return_To_Zero()                      // 安全回零
**********************************************************/
