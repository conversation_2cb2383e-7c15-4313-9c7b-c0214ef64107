# 第二个电机速度模式控制详解

## 概述
本系统通过USART2控制第二个Emm_V5.0步进电机驱动器，实现双电机独立控制。第二个电机使用带`_USART`后缀的函数进行控制。

## 硬件连接
```
STM32F103 -> 第二个电机驱动器
PA2 (USART2_TX) -> 驱动器RX
PA3 (USART2_RX) -> 驱动器TX  
GND -> GND
5V -> VCC
```

## 主要控制函数详解

### 1. 电机使能控制
```c
void Emm_V5_En_Control_USART(uint8_t usart_num, uint8_t addr, bool state, bool snF);
```
**参数说明：**
- `usart_num`: 串口号，第二个电机固定使用 `2`
- `addr`: 电机地址，通常为 `1`
- `state`: 使能状态，`true`=使能，`false`=禁用
- `snF`: 同步标志，`false`=立即执行，`true`=等待同步触发

**使用示例：**
```c
// 使能第二个电机
Emm_V5_En_Control_USART(2, 1, true, false);

// 禁用第二个电机  
Emm_V5_En_Control_USART(2, 1, false, false);
```

### 2. 速度模式控制（核心函数）
```c
void Emm_V5_Vel_Control_USART(uint8_t usart_num, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF);
```
**参数说明：**
- `usart_num`: 串口号，第二个电机固定使用 `2`
- `addr`: 电机地址，通常为 `1`
- `dir`: 旋转方向，`0`=顺时针(CW)，`1`=逆时针(CCW)
- `vel`: 目标速度，单位RPM，范围0-5000
- `acc`: 加速度，范围0-255，0表示直接启动
- `snF`: 同步标志，`false`=立即执行，`true`=等待同步触发

**使用示例：**
```c
// 第二个电机顺时针1000RPM，加速度10
Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);

// 第二个电机逆时针800RPM，加速度15
Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);

// 第二个电机同步控制（需要触发同步）
Emm_V5_Vel_Control_USART(2, 1, 0, 1500, 20, true);
```

### 3. 停止控制
```c
void Emm_V5_Stop_Now_USART(uint8_t usart_num, uint8_t addr, bool snF);
```
**参数说明：**
- `usart_num`: 串口号，第二个电机固定使用 `2`
- `addr`: 电机地址，通常为 `1`
- `snF`: 同步标志，`false`=立即停止，`true`=等待同步触发

**使用示例：**
```c
// 立即停止第二个电机
Emm_V5_Stop_Now_USART(2, 1, false);

// 同步停止第二个电机
Emm_V5_Stop_Now_USART(2, 1, true);
```

### 4. 同步运动触发
```c
void Emm_V5_Synchronous_motion_USART(uint8_t usart_num, uint8_t addr);
```
**参数说明：**
- `usart_num`: 串口号，第二个电机固定使用 `2`
- `addr`: 电机地址，通常为 `1`

**使用示例：**
```c
// 触发第二个电机的同步运动
Emm_V5_Synchronous_motion_USART(2, 1);
```

## 完整控制逻辑示例

### 示例1：基本速度控制
```c
void motor2_basic_control(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);  // 等待驱动器初始化
    
    // 2. 使能第二个电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);   // 等待使能完成
    
    // 3. 启动速度控制 - 顺时针1000RPM
    Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);
    delay_ms(5000);  // 运行5秒
    
    // 4. 改变方向和速度 - 逆时针800RPM
    Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
    delay_ms(3000);  // 运行3秒
    
    // 5. 停止电机
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 6. 禁用电机
    Emm_V5_En_Control_USART(2, 1, false, false);
}
```

### 示例2：双电机同步控制
```c
void dual_motor_sync_control(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能两个电机
    Emm_V5_En_Control(1, true, false);           // 电机1
    Emm_V5_En_Control_USART(2, 1, true, false);  // 电机2
    delay_ms(100);
    
    // 3. 设置同步运动参数
    Emm_V5_Vel_Control(1, 0, 1000, 10, true);        // 电机1，同步标志=true
    Emm_V5_Vel_Control_USART(2, 1, 1, 1000, 10, true); // 电机2，同步标志=true
    
    // 4. 触发同步运动（两个电机同时启动）
    Emm_V5_Synchronous_motion(1);  // 用任一电机触发即可
    delay_ms(5000);
    
    // 5. 同步停止
    Emm_V5_Stop_Now(1, true);           // 电机1，同步标志=true
    Emm_V5_Stop_Now_USART(2, 1, true);  // 电机2，同步标志=true
    Emm_V5_Synchronous_motion(1);       // 触发同步停止
}
```

### 示例3：速度渐变控制
```c
void motor2_speed_ramp(void)
{
    // 初始化
    board_init();
    delay_ms(2000);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 速度从200RPM逐渐增加到2000RPM
    for(uint16_t speed = 200; speed <= 2000; speed += 200)
    {
        Emm_V5_Vel_Control_USART(2, 1, 0, speed, 5, false);
        delay_ms(2000);  // 每个速度运行2秒
    }
    
    // 停止
    Emm_V5_Stop_Now_USART(2, 1, false);
}
```

## 便捷宏定义使用

为了简化代码，可以使用配置文件中的宏定义：

```c
#include "motor_config.h"

void simplified_control(void)
{
    // 使用便捷宏
    DUAL_MOTOR_ENABLE();                                    // 使能双电机
    MOTOR2_VEL_CONTROL(MOTOR_DIR_CW, 1000, 10, false);    // 电机2顺时针
    delay_ms(5000);
    MOTOR2_STOP();                                         // 停止电机2
    DUAL_MOTOR_DISABLE();                                  // 禁用双电机
}
```

## 注意事项

1. **初始化顺序**：必须先调用`board_init()`初始化系统
2. **使能延时**：使能电机后需要延时100ms等待驱动器响应
3. **速度范围**：速度范围0-5000RPM，超出范围可能导致异常
4. **加速度设置**：加速度0表示直接启动，1-255为渐进启动
5. **同步控制**：使用同步功能时，必须调用`Emm_V5_Synchronous_motion`触发
6. **串口冲突**：确保电机地址在不同串口上可以相同，但同一串口上必须不同

## 故障排除

1. **电机无响应**：检查硬件连接和使能状态
2. **速度不准确**：检查驱动器细分设置和参数配置
3. **通信异常**：检查波特率设置和串口初始化
4. **同步失效**：确保正确设置同步标志并触发同步运动
