# 电机1和电机2回零函数对照表

## 📋 函数分类总览

| 功能类别 | 电机1函数 | 电机2函数 | 双电机函数 |
|----------|-----------|-----------|------------|
| **基础回零** | Motor1_Return_To_Zero | Motor2_Return_To_Zero | Dual_Motor_Sync_Return_To_Zero |
| **快速回零** | Motor1_Fast_Return_To_Zero | Motor2_Fast_Return_To_Zero | - |
| **慢速回零** | Motor1_Slow_Return_To_Zero | Motor2_Slow_Return_To_Zero | - |
| **带参数回零** | Motor1_Return_To_Zero_WithParams | Motor2_Return_To_Zero_WithParams | Dual_Motor_Sync_Return_To_Zero_WithParams |
| **同步回零** | Motor1_Sync_Return_To_Zero | Motor2_Sync_Return_To_Zero | - |
| **特殊功能** | - | Motor2_Multi_Address_Return_To_Zero | - |

## 🔧 详细函数对照

### 1. 基础回零函数

#### 电机1回零
```c
void Motor1_Return_To_Zero(void);
```
**功能**：电机1基础回零，使用默认参数（800RPM，加速度10）
**使用**：
```c
Motor1_Return_To_Zero();  // 电机1回到零点位置
```

#### 电机2回零
```c
void Motor2_Return_To_Zero(void);
```
**功能**：电机2基础回零，使用默认参数（800RPM，加速度10）
**使用**：
```c
Motor2_Return_To_Zero();  // 电机2回到零点位置
```

### 2. 快速回零函数

#### 电机1快速回零
```c
void Motor1_Fast_Return_To_Zero(void);
```
**功能**：电机1快速回零（1200RPM，加速度20）
**特点**：速度快，适合远距离回零
**使用**：
```c
Motor1_Fast_Return_To_Zero();  // 电机1快速回零
```

#### 电机2快速回零
```c
void Motor2_Fast_Return_To_Zero(void);
```
**功能**：电机2快速回零（1200RPM，加速度20）
**特点**：速度快，适合远距离回零
**使用**：
```c
Motor2_Fast_Return_To_Zero();  // 电机2快速回零
```

### 3. 慢速精确回零函数

#### 电机1慢速回零
```c
void Motor1_Slow_Return_To_Zero(void);
```
**功能**：电机1慢速精确回零（400RPM，加速度5）
**特点**：速度慢，精度高，适合精确定位
**使用**：
```c
Motor1_Slow_Return_To_Zero();  // 电机1慢速精确回零
```

#### 电机2慢速回零
```c
void Motor2_Slow_Return_To_Zero(void);
```
**功能**：电机2慢速精确回零（400RPM，加速度5）
**特点**：速度慢，精度高，适合精确定位
**使用**：
```c
Motor2_Slow_Return_To_Zero();  // 电机2慢速精确回零
```

### 4. 带参数回零函数

#### 电机1带参数回零
```c
void Motor1_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration);
```
**功能**：电机1自定义参数回零
**参数**：
- `speed`：回零速度（100-3000 RPM）
- `acceleration`：回零加速度（3-50）

**使用**：
```c
Motor1_Return_To_Zero_WithParams(600, 8);   // 600RPM，加速度8
Motor1_Return_To_Zero_WithParams(1500, 20); // 1500RPM，加速度20
```

#### 电机2带参数回零
```c
void Motor2_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration);
```
**功能**：电机2自定义参数回零
**参数**：
- `speed`：回零速度（100-3000 RPM）
- `acceleration`：回零加速度（3-50）

**使用**：
```c
Motor2_Return_To_Zero_WithParams(600, 8);   // 600RPM，加速度8
Motor2_Return_To_Zero_WithParams(1500, 20); // 1500RPM，加速度20
```

### 5. 同步回零函数

#### 电机1同步回零
```c
void Motor1_Sync_Return_To_Zero(void);
```
**功能**：设置电机1同步回零参数，需要外部触发
**使用**：
```c
Motor1_Sync_Return_To_Zero();     // 设置同步参数
Motor2_Sync_Return_To_Zero();     // 设置同步参数
Emm_V5_Synchronous_motion(1);     // 触发同步运动
```

#### 电机2同步回零
```c
void Motor2_Sync_Return_To_Zero(void);
```
**功能**：设置电机2同步回零参数，需要外部触发
**使用**：
```c
Motor1_Sync_Return_To_Zero();     // 设置同步参数
Motor2_Sync_Return_To_Zero();     // 设置同步参数
Emm_V5_Synchronous_motion(1);     // 触发同步运动
```

### 6. 双电机回零函数

#### 双电机独立回零
```c
void Dual_Motor_Independent_Return_To_Zero(void);
```
**功能**：先回零电机1，再回零电机2
**使用**：
```c
Dual_Motor_Independent_Return_To_Zero();  // 双电机依次回零
```

#### 双电机同步回零
```c
void Dual_Motor_Sync_Return_To_Zero(void);
```
**功能**：两个电机同时开始回零运动
**使用**：
```c
Dual_Motor_Sync_Return_To_Zero();  // 双电机同步回零
```

#### 双电机带参数同步回零
```c
void Dual_Motor_Sync_Return_To_Zero_WithParams(uint16_t speed, uint8_t acceleration);
```
**功能**：双电机使用自定义参数同步回零
**使用**：
```c
Dual_Motor_Sync_Return_To_Zero_WithParams(800, 12);  // 800RPM，加速度12
```

### 7. 特殊功能函数

#### 电机2多地址回零（解决地址问题）
```c
void Motor2_Multi_Address_Return_To_Zero(void);
```
**功能**：尝试地址1-5，解决电机2地址设置问题
**使用场景**：电机2无响应时使用
**使用**：
```c
Motor2_Multi_Address_Return_To_Zero();  // 尝试多个地址回零
```

### 8. 高级功能函数

#### 智能回零
```c
void Smart_Return_To_Zero(uint8_t motor_num, uint32_t current_position);
```
**功能**：根据距离自动选择最优参数
**参数**：
- `motor_num`：电机编号（1或2）
- `current_position`：当前位置（脉冲数）

**使用**：
```c
Smart_Return_To_Zero(1, 1600);  // 电机1，当前位置1600脉冲（180°）
Smart_Return_To_Zero(2, 3200);  // 电机2，当前位置3200脉冲（360°）
```

#### 安全回零
```c
bool Safe_Return_To_Zero(uint8_t motor_num);
```
**功能**：带错误检查的回零函数
**返回值**：true=成功，false=失败
**使用**：
```c
if(Safe_Return_To_Zero(1)) {
    // 电机1回零成功
} else {
    // 电机1回零失败，进行错误处理
}
```

## 🎯 便捷宏定义

### 快速调用宏
```c
MOTOR1_RETURN_ZERO()        // 等同于 Motor1_Return_To_Zero()
MOTOR2_RETURN_ZERO()        // 等同于 Motor2_Return_To_Zero()
DUAL_MOTOR_RETURN_ZERO()    // 等同于 Dual_Motor_Sync_Return_To_Zero()
```

### 速度选择宏
```c
MOTOR1_FAST_RETURN()        // 等同于 Motor1_Fast_Return_To_Zero()
MOTOR1_SLOW_RETURN()        // 等同于 Motor1_Slow_Return_To_Zero()
MOTOR2_FAST_RETURN()        // 等同于 Motor2_Fast_Return_To_Zero()
MOTOR2_SLOW_RETURN()        // 等同于 Motor2_Slow_Return_To_Zero()
```

### 同步回零宏
```c
COMPLETE_SYNC_RETURN_ZERO() // 完整的同步回零过程
```

## 📊 参数对照表

| 回零类型 | 速度(RPM) | 加速度 | 等待时间(ms) | 适用场景 |
|----------|-----------|--------|--------------|----------|
| **基础回零** | 800 | 10 | 2000 | 通用场景 |
| **快速回零** | 1200 | 20 | 1500 | 远距离回零 |
| **慢速回零** | 400 | 5 | 3000 | 精确定位 |
| **自定义回零** | 100-3000 | 3-50 | 计算值 | 特殊需求 |

## 💡 使用建议

### 选择指南
1. **日常使用**：使用基础回零函数
2. **远距离回零**：使用快速回零函数
3. **精确定位**：使用慢速回零函数
4. **特殊需求**：使用带参数回零函数
5. **双电机协调**：使用同步回零函数
6. **电机2问题**：使用多地址回零函数

### 调用顺序
```c
// 1. 系统初始化
board_init();
delay_ms(2000);

// 2. 设置零点（首次使用）
Emm_V5_Reset_CurPos_To_Zero(1);
Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);

// 3. 电机运动
// ... 电机运动代码 ...

// 4. 回零操作
Motor1_Return_To_Zero();  // 或其他回零函数
Motor2_Return_To_Zero();
```

### 错误处理
```c
// 使用安全回零函数
if(!Safe_Return_To_Zero(1)) {
    // 电机1回零失败处理
}

if(!Safe_Return_To_Zero(2)) {
    // 电机2回零失败，尝试多地址
    Motor2_Multi_Address_Return_To_Zero();
}
```

通过这个对照表，您可以快速找到适合的回零函数，并了解每个函数的具体用法和特点。
