#ifndef __MOTOR_CONFIG_H
#define __MOTOR_CONFIG_H

/**********************************************************
***	双电机配置文件
***	作者：基于ZHANGDATOU原始代码扩展
***	功能：统一管理双电机系统的配置参数
**********************************************************/

// 电机地址配置
#define MOTOR1_ADDR         1    // 电机1地址 (USART1)
#define MOTOR2_ADDR         1    // 电机2地址 (USART2)

// 串口配置
#define MOTOR1_USART_NUM    1    // 电机1使用USART1
#define MOTOR2_USART_NUM    2    // 电机2使用USART2

// 默认速度参数
#define MOTOR1_DEFAULT_VEL  1000 // 电机1默认速度 (RPM)
#define MOTOR2_DEFAULT_VEL  800  // 电机2默认速度 (RPM)

// 默认加速度参数
#define MOTOR1_DEFAULT_ACC  10   // 电机1默认加速度
#define MOTOR2_DEFAULT_ACC  15   // 电机2默认加速度

// 电机方向定义
#define MOTOR_DIR_CW        0    // 顺时针
#define MOTOR_DIR_CCW       1    // 逆时针

// 同步控制标志
#define SYNC_ENABLE         true  // 启用同步
#define SYNC_DISABLE        false // 禁用同步

// 存储标志
#define SAVE_TO_FLASH       true  // 保存到Flash
#define NO_SAVE             false // 不保存

// 位置控制参数
#define MOTOR_STEPS_PER_REV 3200  // 每圈脉冲数 (根据驱动器细分设置)

// 安全参数
#define MAX_VELOCITY        5000  // 最大速度限制 (RPM)
#define MAX_ACCELERATION    255   // 最大加速度限制
#define EMERGENCY_STOP_TIME 100   // 紧急停止延时 (ms)

// 通信超时设置
#define COMM_TIMEOUT_MS     1000  // 通信超时时间 (ms)
#define RETRY_COUNT         3     // 重试次数

// 电机状态定义
typedef enum {
    MOTOR_STATE_IDLE = 0,     // 空闲
    MOTOR_STATE_RUNNING,      // 运行中
    MOTOR_STATE_STOPPING,     // 停止中
    MOTOR_STATE_ERROR         // 错误状态
} MotorState_t;

// 电机配置结构体
typedef struct {
    uint8_t addr;             // 电机地址
    uint8_t usart_num;        // 使用的串口号
    uint16_t default_vel;     // 默认速度
    uint8_t default_acc;      // 默认加速度
    uint8_t default_dir;      // 默认方向
    MotorState_t state;       // 当前状态
} MotorConfig_t;

// 全局电机配置
extern MotorConfig_t motor1_config;
extern MotorConfig_t motor2_config;

// 配置函数声明
void motor_config_init(void);
void motor_set_config(uint8_t motor_num, MotorConfig_t *config);
MotorConfig_t* motor_get_config(uint8_t motor_num);
void motor_save_config(void);
void motor_load_config(void);

// 便捷控制宏定义
#define MOTOR1_VEL_CONTROL(dir, vel, acc, sync) \
    Emm_V5_Vel_Control(MOTOR1_ADDR, dir, vel, acc, sync)

#define MOTOR2_VEL_CONTROL(dir, vel, acc, sync) \
    Emm_V5_Vel_Control_USART(MOTOR2_USART_NUM, MOTOR2_ADDR, dir, vel, acc, sync)

#define MOTOR1_ENABLE(state) \
    Emm_V5_En_Control(MOTOR1_ADDR, state, SYNC_DISABLE)

#define MOTOR2_ENABLE(state) \
    Emm_V5_En_Control_USART(MOTOR2_USART_NUM, MOTOR2_ADDR, state, SYNC_DISABLE)

#define MOTOR1_STOP() \
    Emm_V5_Stop_Now(MOTOR1_ADDR, SYNC_DISABLE)

#define MOTOR2_STOP() \
    Emm_V5_Stop_Now_USART(MOTOR2_USART_NUM, MOTOR2_ADDR, SYNC_DISABLE)

#define MOTOR1_RESET_POS() \
    Emm_V5_Reset_CurPos_To_Zero(MOTOR1_ADDR)

#define MOTOR2_RESET_POS() \
    Emm_V5_Reset_CurPos_To_Zero_USART(MOTOR2_USART_NUM, MOTOR2_ADDR)

// 双电机同步控制宏
#define DUAL_MOTOR_ENABLE() \
    do { \
        MOTOR1_ENABLE(true); \
        MOTOR2_ENABLE(true); \
        delay_ms(100); \
    } while(0)

#define DUAL_MOTOR_DISABLE() \
    do { \
        MOTOR1_ENABLE(false); \
        MOTOR2_ENABLE(false); \
    } while(0)

#define DUAL_MOTOR_STOP() \
    do { \
        MOTOR1_STOP(); \
        MOTOR2_STOP(); \
    } while(0)

#define DUAL_MOTOR_RESET_POS() \
    do { \
        MOTOR1_RESET_POS(); \
        MOTOR2_RESET_POS(); \
    } while(0)

// 安全检查宏
#define CHECK_VELOCITY(vel) \
    ((vel) <= MAX_VELOCITY ? (vel) : MAX_VELOCITY)

#define CHECK_ACCELERATION(acc) \
    ((acc) <= MAX_ACCELERATION ? (acc) : MAX_ACCELERATION)

// 调试输出宏 (需要根据实际调试方式修改)
#ifdef DEBUG_MODE
#define MOTOR_DEBUG(fmt, ...) printf("[MOTOR] " fmt "\r\n", ##__VA_ARGS__)
#else
#define MOTOR_DEBUG(fmt, ...)
#endif

#endif /* __MOTOR_CONFIG_H */
