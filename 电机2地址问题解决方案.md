# 电机2地址问题解决方案

## 🎯 问题分析

### ❌ **问题现象**
- 电机2干脆不动了
- 原因：在main.c中对电机2的每个操作都写了两遍（地址1和地址2）
- 结果：命令冲突，电机2无法正确响应

### ✅ **解决方案**
- 统一电机2的地址设置
- 在代码中只使用一个确定的地址
- 提供简单的地址切换方法

## 🔧 修改内容

### 1. 添加地址宏定义
```c
// 电机2地址设置（根据实际情况修改）
#define MOTOR2_ADDR 2  // 如果电机2不动，尝试改为1
```

### 2. 统一所有电机2操作
```c
// 使能
Emm_V5_En_Control_USART(2, MOTOR2_ADDR, true, false);

// 复位
Emm_V5_Reset_CurPos_To_Zero_USART(2, MOTOR2_ADDR);

// 控制
Emm_V5_Pos_Control_USART(2, MOTOR2_ADDR, 1, 1600, 15, 1600, false, false);
```

### 3. 简化回零函数
```c
void dual_motor_return_to_zero(void)
{
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, MOTOR2_ADDR, true, false);
    delay_ms(200);
    
    // 复位位置到零点
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, MOTOR2_ADDR);
    delay_ms(200);
    
    // 分别回零
    Emm_V5_Pos_Control(1, 0, 800, 10, 0, true, false);
    delay_ms(100);
    Emm_V5_Pos_Control_USART(2, MOTOR2_ADDR, 0, 800, 10, 0, true, false);
    
    // 等待回零完成
    delay_ms(2000);
}
```

## 🚀 使用方法

### 步骤1：确定电机2的正确地址

#### 方法1：检查硬件设置
- 查看电机2驱动器上的拨码开关
- 常见设置：
  - 地址1：拨码开关全部OFF
  - 地址2：第1位ON，其他OFF
  - 地址3：第2位ON，其他OFF

#### 方法2：代码测试
1. **先尝试地址2**（默认设置）：
   ```c
   #define MOTOR2_ADDR 2
   ```

2. **编译下载测试**：
   - 如果电机2能正常运动，说明地址2正确
   - 如果电机2仍不动，进行步骤3

3. **尝试地址1**：
   ```c
   #define MOTOR2_ADDR 1
   ```

4. **再次编译下载测试**：
   - 如果电机2能正常运动，说明地址1正确
   - 如果仍不动，检查硬件连接

### 步骤2：编译和测试
1. 在Keil5中编译工程
2. 下载到STM32
3. 观察电机2是否正常运动
4. 测试回零功能：`dual_motor_return_to_zero()`

### 步骤3：验证功能
- ✅ 电机2应该能正常运动
- ✅ 电机2应该能正常回零
- ✅ 两个电机应该能协调工作

## 🔍 故障排除

### 如果电机2仍然不动

#### 1. 检查地址设置
```c
// 尝试不同地址
#define MOTOR2_ADDR 1  // 或者 2, 3, 4
```

#### 2. 检查硬件连接
- PA2 (USART2_TX) -> 电机2驱动器RX
- PA3 (USART2_RX) -> 电机2驱动器TX
- GND -> GND
- 5V -> VCC

#### 3. 检查驱动器状态
- 电源LED是否亮起
- 通信LED是否闪烁
- 错误LED是否亮起

#### 4. 检查电源供应
- 电机2驱动器是否正常上电
- 电源容量是否足够
- 电源线是否接触良好

### 如果地址1和地址2都不行

#### 尝试其他地址
```c
#define MOTOR2_ADDR 3  // 或者 4, 5
```

#### 检查驱动器型号
- 确认两个驱动器型号一致
- 检查驱动器版本是否兼容
- 查看驱动器说明书确认默认地址

## 💡 最佳实践

### 1. 地址管理
- 使用宏定义统一管理地址
- 避免在代码中硬编码地址
- 为不同项目使用不同的地址配置

### 2. 调试方法
- 先确定地址，再进行功能开发
- 使用示波器检查通信信号
- 逐步测试：使能 -> 移动 -> 回零

### 3. 代码组织
- 所有电机2操作使用统一的地址宏
- 避免重复的命令发送
- 保持代码简洁和一致性

## 📋 总结

### 问题根源
- **重复命令**：对电机2发送了多个地址的相同命令
- **地址冲突**：不同地址的命令相互干扰
- **时序混乱**：多个命令导致时序不可控

### 解决方案
- **统一地址**：使用宏定义统一管理电机2地址
- **简化逻辑**：每个操作只发送一次命令
- **清晰结构**：代码逻辑简单明了

### 最终效果
- ✅ **电机2正常运动**：统一地址后应该能正常工作
- ✅ **回零功能正常**：简化的回零函数应该能正常工作
- ✅ **代码简洁可靠**：逻辑清晰，易于维护

**现在电机2应该能够正常工作了！如果仍有问题，请尝试修改 `MOTOR2_ADDR` 的值。** 🚀
