#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	电机2问题诊断程序
***	用于诊断电机2无法复位的问题
***	逐步测试各个功能模块
**********************************************************/

/**
 * @brief 测试USART2基本发送功能
 */
void test_usart2_basic_send(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 测试USART2基本发送
    uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04};
    
    for(int i = 0; i < 10; i++) {
        // 直接使用USART2发送测试数据
        usart2_SendCmd(test_data, 4);
        delay_ms(500);
    }
}

/**
 * @brief 测试电机2使能功能
 */
void test_motor2_enable(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 测试电机2使能
    for(int i = 0; i < 5; i++) {
        // 使能电机2
        Emm_V5_En_Control_USART(2, 1, true, false);
        delay_ms(1000);
        
        // 禁用电机2
        Emm_V5_En_Control_USART(2, 1, false, false);
        delay_ms(1000);
    }
}

/**
 * @brief 测试电机2位置复位功能（详细版本）
 */
void test_motor2_reset_detailed(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 先使能电机2
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(500);
    
    // 测试位置复位 - 多次尝试
    for(int i = 0; i < 3; i++) {
        // 方法1：使用Reset_CurPos_To_Zero
        Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
        delay_ms(200);
        
        // 方法2：使用Origin_Set_O
        Emm_V5_Origin_Set_O_USART(2, 1, false);  // 不保存到Flash
        delay_ms(200);
        
        // 延时观察
        delay_ms(1000);
    }
}

/**
 * @brief 对比测试电机1和电机2
 */
void test_motor1_vs_motor2_comparison(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);           // 电机1
    Emm_V5_En_Control_USART(2, 1, true, false);  // 电机2
    delay_ms(500);
    
    for(int cycle = 0; cycle < 3; cycle++) {
        // 电机1复位
        Emm_V5_Reset_CurPos_To_Zero(1);
        delay_ms(200);
        
        // 电机2复位
        Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
        delay_ms(200);
        
        // 电机1移动
        Emm_V5_Pos_Control(1, 0, 500, 10, 800, false, false);  // 90°
        delay_ms(1000);
        
        // 电机2移动
        Emm_V5_Pos_Control_USART(2, 1, 0, 500, 10, 800, false, false);  // 90°
        delay_ms(1000);
        
        // 电机1回零
        Emm_V5_Pos_Control(1, 0, 500, 10, 0, true, false);
        delay_ms(1000);
        
        // 电机2回零
        Emm_V5_Pos_Control_USART(2, 1, 0, 500, 10, 0, true, false);
        delay_ms(1000);
        
        delay_ms(2000);  // 循环间隔
    }
}

/**
 * @brief 测试不同的电机2地址
 */
void test_motor2_different_addresses(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 测试不同的电机地址
    uint8_t addresses[] = {1, 2, 3, 4, 5};
    
    for(int i = 0; i < 5; i++) {
        uint8_t addr = addresses[i];
        
        // 使能电机
        Emm_V5_En_Control_USART(2, addr, true, false);
        delay_ms(200);
        
        // 复位位置
        Emm_V5_Reset_CurPos_To_Zero_USART(2, addr);
        delay_ms(200);
        
        // 小幅移动测试
        Emm_V5_Pos_Control_USART(2, addr, 0, 300, 5, 200, false, false);
        delay_ms(1000);
        
        // 回零测试
        Emm_V5_Pos_Control_USART(2, addr, 0, 300, 5, 0, true, false);
        delay_ms(1000);
        
        // 禁用电机
        Emm_V5_En_Control_USART(2, addr, false, false);
        delay_ms(500);
    }
}

/**
 * @brief 手动构造电机2复位命令
 */
void test_motor2_manual_reset_command(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 手动构造复位命令
    uint8_t reset_cmd[4];
    reset_cmd[0] = 1;      // 电机地址
    reset_cmd[1] = 0x0A;   // 复位命令码
    reset_cmd[2] = 0x6D;   // 命令参数
    reset_cmd[3] = 0x6B;   // 校验字节
    
    // 先使能电机2
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(500);
    
    // 发送手动构造的复位命令
    for(int i = 0; i < 5; i++) {
        usart2_SendCmd(reset_cmd, 4);
        delay_ms(500);
    }
    
    // 测试移动
    Emm_V5_Pos_Control_USART(2, 1, 0, 500, 10, 400, false, false);
    delay_ms(1000);
}

/**
 * @brief 测试电机2的原点设置功能
 */
void test_motor2_origin_functions(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能电机2
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(500);
    
    // 测试各种原点相关功能
    for(int i = 0; i < 3; i++) {
        // 1. 复位当前位置
        Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
        delay_ms(300);
        
        // 2. 设置原点（不保存）
        Emm_V5_Origin_Set_O_USART(2, 1, false);
        delay_ms(300);
        
        // 3. 移动一段距离
        Emm_V5_Pos_Control_USART(2, 1, 0, 600, 10, 600, false, false);
        delay_ms(1200);
        
        // 4. 使用绝对位置回零
        Emm_V5_Pos_Control_USART(2, 1, 0, 600, 10, 0, true, false);
        delay_ms(1200);
        
        delay_ms(1000);  // 循环间隔
    }
}

/**
 * @brief 检查USART2中断和接收功能
 */
void test_usart2_interrupt_and_receive(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能电机2
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(500);
    
    // 发送读取命令并检查响应
    for(int i = 0; i < 5; i++) {
        // 发送读取系统参数命令
        Emm_V5_Read_Sys_Params_USART(2, 1, S_VEL);  // 读取速度
        delay_ms(100);
        
        // 检查是否收到响应
        if(rxFrameFlag2) {
            rxFrameFlag2 = false;
            // 收到响应，说明通信正常
            // 可以在这里添加LED指示或其他反馈
        }
        
        delay_ms(1000);
    }
}

/**
 * @brief 主函数 - 选择测试项目
 */
int main(void)
{
    // 选择要运行的测试（取消注释对应的测试）
    
    // test_usart2_basic_send();           // 测试USART2基本发送
    // test_motor2_enable();               // 测试电机2使能
    // test_motor2_reset_detailed();       // 测试电机2复位（详细）
    test_motor1_vs_motor2_comparison();    // 对比测试（推荐先运行这个）
    // test_motor2_different_addresses();  // 测试不同地址
    // test_motor2_manual_reset_command();  // 手动构造命令
    // test_motor2_origin_functions();     // 测试原点功能
    // test_usart2_interrupt_and_receive(); // 测试中断接收
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	诊断步骤说明：
***
***	1. 首先运行 test_motor1_vs_motor2_comparison()
***	   观察两个电机的行为差异
***
***	2. 如果电机2完全无响应，运行 test_usart2_basic_send()
***	   检查USART2基本通信功能
***
***	3. 如果通信正常但复位无效，运行 test_motor2_reset_detailed()
***	   测试不同的复位方法
***
***	4. 如果怀疑地址问题，运行 test_motor2_different_addresses()
***	   尝试不同的电机地址
***
***	5. 如果需要底层调试，运行 test_motor2_manual_reset_command()
***	   手动构造和发送命令
***
***	可能的问题原因：
***	- 硬件连接问题（PA2/PA3接线）
***	- 电机驱动器地址设置
***	- 电源供应问题
***	- 驱动器参数配置
***	- USART2初始化问题
***
***	调试建议：
***	- 使用示波器检查PA2的TX信号
***	- 检查电机驱动器的LED指示
***	- 确认两个驱动器的型号和设置一致
***	- 检查电源和地线连接
**********************************************************/
