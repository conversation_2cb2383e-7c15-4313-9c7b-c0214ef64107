# 编译说明和问题解决

## 编译顺序要求

### 1. 头文件包含路径
确保编译器包含路径设置正确：
```
- ./APP
- ./BSP  
- ./DRIVERS
- ./CMSIS
- ./LIB/STM32F10x_StdPeriph_Lib_V3.5.0/Libraries/STM32F10x_StdPeriph_Driver/inc
- ./LIB/STM32F10x_StdPeriph_Lib_V3.5.0/Libraries/CMSIS/CM3/CoreSupport
- ./LIB/STM32F10x_StdPeriph_Lib_V3.5.0/Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x
```

### 2. 编译文件顺序
建议的编译顺序：
```
1. DRIVERS/fifo.c          # 首先编译FIFO模块
2. BSP/usart.c             # 然后编译串口模块
3. BSP/board.c             # 再编译板级初始化
4. BSP/Emm_V5.c            # 编译电机控制
5. BSP/motor_config.c      # 编译配置管理
6. DRIVERS/delay.c         # 编译延时模块
7. APP/main.c              # 最后编译主程序
```

## 常见编译错误及解决方案

### 错误1: "use of undeclared identifier rxFIFO1"

**原因**: fifo.h未正确包含或fifo.c未编译

**解决方案**:
1. 检查fifo.h是否在包含路径中
2. 确保fifo.c在board.c之前编译
3. 验证fifo.h中的extern声明：
```c
extern __IO FIFO_t rxFIFO1; // USART1 FIFO
extern __IO FIFO_t rxFIFO2; // USART2 FIFO
```

### 错误2: "__IO未定义"

**原因**: STM32标准库头文件未正确包含

**解决方案**:
确保stm32f10x.h被正确包含，__IO宏定义在该文件中。

### 错误3: "USART2相关函数未声明"

**原因**: usart.h未正确包含新增的函数声明

**解决方案**:
检查usart.h中是否包含：
```c
void usart2_SendCmd(__IO uint8_t *cmd, uint8_t len);
void usart2_SendByte(uint16_t data);
```

### 错误4: "Emm_V5_*_USART函数未声明"

**原因**: Emm_V5.h未包含新增的USART版本函数

**解决方案**:
检查Emm_V5.h中是否包含所有新增函数声明。

## Keil uVision 5 项目设置

### 1. 包含路径设置
在Project -> Options for Target -> C/C++ -> Include Paths中添加：
```
.\APP
.\BSP
.\DRIVERS
.\CMSIS
.\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\Libraries\STM32F10x_StdPeriph_Driver\inc
.\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\Libraries\CMSIS\CM3\CoreSupport
.\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\Libraries\CMSIS\CM3\DeviceSupport\ST\STM32F10x
```

### 2. 预定义宏
在Project -> Options for Target -> C/C++ -> Define中添加：
```
STM32F10X_HD
USE_STDPERIPH_DRIVER
```

### 3. 文件分组
建议的项目文件分组：
```
Application/User
├── main.c
├── stm32f10x_conf.h
└── stm32f10x_it.c

BSP
├── board.c/h
├── usart.c/h
├── Emm_V5.c/h
└── motor_config.c/h

Drivers
├── fifo.c/h
└── delay.c/h

CMSIS
├── core_cm3.c/h
├── system_stm32f10x.c/h
└── startup_stm32f10x_hd.s

StdPeriph_Driver
└── (标准库文件)
```

## 验证编译成功

编译成功后应该看到：
```
Build target 'Target 1'
compiling fifo.c...
compiling usart.c...
compiling board.c...
compiling Emm_V5.c...
compiling motor_config.c...
compiling delay.c...
compiling main.c...
linking...
Program Size: Code=XXXX RO-data=XXXX RW-data=XXXX ZI-data=XXXX
"STM32_UART_CMD.axf" - 0 Error(s), 0 Warning(s).
```

## 调试建议

如果仍有编译问题：

1. **清理重建**: 使用Project -> Clean Targets清理后重新编译
2. **检查语法**: 确保所有分号、括号匹配
3. **逐个编译**: 单独编译每个.c文件定位问题
4. **查看详细错误**: 在Build Output窗口查看完整错误信息

## 运行时验证

编译成功后，可以通过以下方式验证功能：
1. 下载程序到STM32
2. 连接两个电机驱动器
3. 观察电机运行状态
4. 使用示波器检查串口信号
