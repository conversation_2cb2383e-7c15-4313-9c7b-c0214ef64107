#include "motor_config.h"
#include "delay.h"

/**********************************************************
***	双电机配置文件实现
***	作者：基于ZHANGDATOU原始代码扩展
***	功能：统一管理双电机系统的配置参数
**********************************************************/

// 全局电机配置实例
MotorConfig_t motor1_config = {
    .addr = MOTOR1_ADDR,
    .usart_num = MOTOR1_USART_NUM,
    .default_vel = MOTOR1_DEFAULT_VEL,
    .default_acc = MOTOR1_DEFAULT_ACC,
    .default_dir = MOTOR_DIR_CW,
    .state = MOTOR_STATE_IDLE
};

MotorConfig_t motor2_config = {
    .addr = MOTOR2_ADDR,
    .usart_num = MOTOR2_USART_NUM,
    .default_vel = MOTOR2_DEFAULT_VEL,
    .default_acc = MOTOR2_DEFAULT_ACC,
    .default_dir = MOTOR_DIR_CCW,
    .state = MOTOR_STATE_IDLE
};

/**
 * @brief 初始化电机配置
 */
void motor_config_init(void)
{
    // 加载保存的配置（如果有的话）
    motor_load_config();
    
    MOTOR_DEBUG("Motor configuration initialized");
    MOTOR_DEBUG("Motor1: addr=%d, usart=%d, vel=%d, acc=%d", 
                motor1_config.addr, motor1_config.usart_num, 
                motor1_config.default_vel, motor1_config.default_acc);
    MOTOR_DEBUG("Motor2: addr=%d, usart=%d, vel=%d, acc=%d", 
                motor2_config.addr, motor2_config.usart_num, 
                motor2_config.default_vel, motor2_config.default_acc);
}

/**
 * @brief 设置电机配置
 * @param motor_num 电机编号 (1或2)
 * @param config 配置结构体指针
 */
void motor_set_config(uint8_t motor_num, MotorConfig_t *config)
{
    if(config == NULL) return;
    
    // 安全检查
    config->default_vel = CHECK_VELOCITY(config->default_vel);
    config->default_acc = CHECK_ACCELERATION(config->default_acc);
    
    if(motor_num == 1) {
        motor1_config = *config;
        MOTOR_DEBUG("Motor1 config updated");
    } else if(motor_num == 2) {
        motor2_config = *config;
        MOTOR_DEBUG("Motor2 config updated");
    }
}

/**
 * @brief 获取电机配置
 * @param motor_num 电机编号 (1或2)
 * @return 配置结构体指针
 */
MotorConfig_t* motor_get_config(uint8_t motor_num)
{
    if(motor_num == 1) {
        return &motor1_config;
    } else if(motor_num == 2) {
        return &motor2_config;
    }
    return NULL;
}

/**
 * @brief 保存配置到Flash (示例实现)
 * 注意：实际实现需要根据具体的Flash操作函数
 */
void motor_save_config(void)
{
    // TODO: 实现Flash写入功能
    // 这里只是示例，实际需要根据STM32的Flash操作API实现
    
    MOTOR_DEBUG("Motor configuration saved to Flash");
}

/**
 * @brief 从Flash加载配置 (示例实现)
 * 注意：实际实现需要根据具体的Flash操作函数
 */
void motor_load_config(void)
{
    // TODO: 实现Flash读取功能
    // 这里只是示例，实际需要根据STM32的Flash操作API实现
    
    MOTOR_DEBUG("Motor configuration loaded from Flash");
}

/**
 * @brief 高级电机控制函数 - 带配置的速度控制
 * @param motor_num 电机编号 (1或2)
 * @param dir 方向 (0:CW, 1:CCW)
 * @param vel 速度 (RPM)，0表示使用默认速度
 * @param acc 加速度，0表示使用默认加速度
 * @param sync 同步标志
 */
void motor_vel_control_ex(uint8_t motor_num, uint8_t dir, uint16_t vel, uint8_t acc, bool sync)
{
    MotorConfig_t *config = motor_get_config(motor_num);
    if(config == NULL) return;
    
    // 使用默认值（如果参数为0）
    if(vel == 0) vel = config->default_vel;
    if(acc == 0) acc = config->default_acc;
    
    // 安全检查
    vel = CHECK_VELOCITY(vel);
    acc = CHECK_ACCELERATION(acc);
    
    // 更新状态
    config->state = MOTOR_STATE_RUNNING;
    
    // 发送控制命令
    if(motor_num == 1) {
        Emm_V5_Vel_Control(config->addr, dir, vel, acc, sync);
    } else if(motor_num == 2) {
        Emm_V5_Vel_Control_USART(config->usart_num, config->addr, dir, vel, acc, sync);
    }
    
    MOTOR_DEBUG("Motor%d: vel=%d, acc=%d, dir=%d, sync=%d", motor_num, vel, acc, dir, sync);
}

/**
 * @brief 高级电机控制函数 - 带配置的位置控制
 * @param motor_num 电机编号 (1或2)
 * @param dir 方向 (0:CW, 1:CCW)
 * @param vel 速度 (RPM)，0表示使用默认速度
 * @param acc 加速度，0表示使用默认加速度
 * @param steps 步数
 * @param relative 相对位置标志
 * @param sync 同步标志
 */
void motor_pos_control_ex(uint8_t motor_num, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t steps, bool relative, bool sync)
{
    MotorConfig_t *config = motor_get_config(motor_num);
    if(config == NULL) return;
    
    // 使用默认值（如果参数为0）
    if(vel == 0) vel = config->default_vel;
    if(acc == 0) acc = config->default_acc;
    
    // 安全检查
    vel = CHECK_VELOCITY(vel);
    acc = CHECK_ACCELERATION(acc);
    
    // 更新状态
    config->state = MOTOR_STATE_RUNNING;
    
    // 发送控制命令
    if(motor_num == 1) {
        Emm_V5_Pos_Control(config->addr, dir, vel, acc, steps, relative, sync);
    } else if(motor_num == 2) {
        Emm_V5_Pos_Control_USART(config->usart_num, config->addr, dir, vel, acc, steps, relative, sync);
    }
    
    MOTOR_DEBUG("Motor%d: pos control, steps=%ld, vel=%d, acc=%d", motor_num, steps, vel, acc);
}

/**
 * @brief 停止电机并更新状态
 * @param motor_num 电机编号 (1或2)
 * @param sync 同步标志
 */
void motor_stop_ex(uint8_t motor_num, bool sync)
{
    MotorConfig_t *config = motor_get_config(motor_num);
    if(config == NULL) return;
    
    // 更新状态
    config->state = MOTOR_STATE_STOPPING;
    
    // 发送停止命令
    if(motor_num == 1) {
        Emm_V5_Stop_Now(config->addr, sync);
    } else if(motor_num == 2) {
        Emm_V5_Stop_Now_USART(config->usart_num, config->addr, sync);
    }
    
    // 延时后更新为空闲状态
    delay_ms(EMERGENCY_STOP_TIME);
    config->state = MOTOR_STATE_IDLE;
    
    MOTOR_DEBUG("Motor%d stopped", motor_num);
}

/**
 * @brief 紧急停止所有电机
 */
void motor_emergency_stop(void)
{
    MOTOR_DEBUG("Emergency stop activated!");
    
    // 立即停止所有电机
    DUAL_MOTOR_STOP();
    
    // 更新状态
    motor1_config.state = MOTOR_STATE_IDLE;
    motor2_config.state = MOTOR_STATE_IDLE;
    
    // 禁用电机
    delay_ms(EMERGENCY_STOP_TIME);
    DUAL_MOTOR_DISABLE();
}

/**
 * @brief 获取电机状态
 * @param motor_num 电机编号 (1或2)
 * @return 电机状态
 */
MotorState_t motor_get_state(uint8_t motor_num)
{
    MotorConfig_t *config = motor_get_config(motor_num);
    if(config == NULL) return MOTOR_STATE_ERROR;
    
    return config->state;
}
