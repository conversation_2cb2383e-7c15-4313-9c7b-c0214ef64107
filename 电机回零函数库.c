#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	电机1和电机2回零函数库（简化版）
***	解决电机2同步回零问题，只保留有效方法
**********************************************************/

// 回零参数配置
#define DEFAULT_RETURN_SPEED        800     // 默认回零速度 (RPM)
#define DEFAULT_RETURN_ACCELERATION 10      // 默认回零加速度

// 电机2可能的地址列表（解决地址问题）
static uint8_t motor2_addresses[] = {1, 2, 3, 4, 5};

/**
 * @brief 电机1回零函数
 * @param 无
 * @retval 无
 */
void Motor1_Return_To_Zero(void)
{
    // 确保电机1使能
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);

    // 使用绝对位置控制回到零点
    Emm_V5_Pos_Control(1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 电机2回零函数（解决地址和同步问题的终极版本）
 * @param 无
 * @retval 无
 */
void Motor2_Return_To_Zero(void)
{
    // 尝试多个地址的使能（解决地址问题）
    for(int i = 0; i < 5; i++)
    {
        uint8_t addr = motor2_addresses[i];
        Emm_V5_En_Control_USART(2, addr, true, false);
        delay_ms(50);
    }

    delay_ms(200);  // 确保使能完成

    // 尝试多个地址的回零（解决地址问题）
    for(int i = 0; i < 5; i++)
    {
        uint8_t addr = motor2_addresses[i];
        Emm_V5_Pos_Control_USART(2, addr, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, false);
        delay_ms(50);
    }

    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 双电机同步回零函数（解决电机2地址问题的终极版本）
 * @param 无
 * @retval 无
 */
void Dual_Motor_Sync_Return_To_Zero(void)
{
    // 使能电机1
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);

    // 使能电机2（尝试多个地址）
    for(int i = 0; i < 5; i++)
    {
        uint8_t addr = motor2_addresses[i];
        Emm_V5_En_Control_USART(2, addr, true, false);
        delay_ms(50);
    }

    delay_ms(200);  // 确保使能完成

    // 设置电机1同步回零参数
    Emm_V5_Pos_Control(1, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, true);
    delay_ms(50);

    // 设置电机2同步回零参数（尝试多个地址）
    for(int i = 0; i < 5; i++)
    {
        uint8_t addr = motor2_addresses[i];
        Emm_V5_Pos_Control_USART(2, addr, 0, DEFAULT_RETURN_SPEED, DEFAULT_RETURN_ACCELERATION, 0, true, true);
        delay_ms(50);
    }

    delay_ms(200);  // 确保命令发送完成

    // 触发同步运动
    Emm_V5_Synchronous_motion(1);
    delay_ms(2000);  // 等待同步回零完成
}

/**********************************************************
***	使用示例函数
**********************************************************/

/**
 * @brief 回零函数使用示例
 * @param 无
 * @retval 无
 */
void Return_To_Zero_Examples(void)
{
    // 示例1：单独回零
    Motor1_Return_To_Zero();
    Motor2_Return_To_Zero();

    delay_ms(1000);

    // 示例2：同步回零
    Dual_Motor_Sync_Return_To_Zero();

    delay_ms(1000);
}

/**********************************************************
***	简化版回零函数说明：
***
***	Motor1_Return_To_Zero()           // 电机1回零
***	Motor2_Return_To_Zero()           // 电机2回零（解决地址问题）
***	Dual_Motor_Sync_Return_To_Zero()  // 双电机同步回零（解决电机2问题）
***
***	使用方法：
***	1. 单独回零：Motor1_Return_To_Zero(); Motor2_Return_To_Zero();
***	2. 同步回零：Dual_Motor_Sync_Return_To_Zero();
**********************************************************/
