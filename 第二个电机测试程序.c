#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	第二个电机专用测试程序
***	解决电机抖动不转的问题
***	硬件连接：PA2(TX2)->驱动器RX, PA3(RX2)->驱动器TX
**********************************************************/

/**
 * @brief 第二个电机基础测试 - 解决抖动问题
 */
void motor2_basic_test(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);  // 等待驱动器上电初始化
    
    // 2. 【关键步骤】使能第二个电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);   // 等待使能完成
    
    // 3. 低速测试 - 顺时针300RPM，加速度5
    Emm_V5_Vel_Control_USART(2, 1, 0, 300, 5, false);
    delay_ms(3000);  // 运行3秒
    
    // 4. 停止
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 5. 中速测试 - 逆时针800RPM，加速度10
    Emm_V5_Vel_Control_USART(2, 1, 1, 800, 10, false);
    delay_ms(3000);  // 运行3秒
    
    // 6. 停止
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 7. 高速测试 - 顺时针1500RPM，加速度15
    Emm_V5_Vel_Control_USART(2, 1, 0, 1500, 15, false);
    delay_ms(2000);  // 运行2秒
    
    // 8. 最终停止并禁用
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(500);
    Emm_V5_En_Control_USART(2, 1, false, false);  // 禁用电机
}

/**
 * @brief 双电机对比测试 - 验证两个电机都正常工作
 */
void dual_motor_compare_test(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 【关键步骤】使能两个电机
    Emm_V5_En_Control(1, true, false);           // 使能电机1
    Emm_V5_En_Control_USART(2, 1, true, false);  // 使能电机2
    delay_ms(100);
    
    // 3. 相同参数测试
    Emm_V5_Vel_Control(1, 0, 1000, 10, false);        // 电机1：顺时针1000RPM
    Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false); // 电机2：顺时针1000RPM
    delay_ms(5000);  // 同时运行5秒
    
    // 4. 停止
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 5. 不同方向测试
    Emm_V5_Vel_Control(1, 0, 800, 10, false);        // 电机1：顺时针800RPM
    Emm_V5_Vel_Control_USART(2, 1, 1, 800, 10, false); // 电机2：逆时针800RPM
    delay_ms(4000);
    
    // 6. 最终停止并禁用
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(500);
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 第二个电机渐进启动测试 - 解决启动抖动
 */
void motor2_smooth_start_test(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 渐进启动：从100RPM开始，每2秒增加200RPM
    uint16_t speeds[] = {100, 300, 500, 800, 1200, 1500};
    uint8_t acc_values[] = {3, 5, 8, 10, 12, 15};
    
    for(int i = 0; i < 6; i++)
    {
        // 顺时针运行
        Emm_V5_Vel_Control_USART(2, 1, 0, speeds[i], acc_values[i], false);
        delay_ms(2000);
        
        // 短暂停止
        Emm_V5_Stop_Now_USART(2, 1, false);
        delay_ms(500);
        
        // 逆时针运行
        Emm_V5_Vel_Control_USART(2, 1, 1, speeds[i], acc_values[i], false);
        delay_ms(2000);
        
        // 停止
        Emm_V5_Stop_Now_USART(2, 1, false);
        delay_ms(500);
    }
    
    // 4. 禁用电机
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 第二个电机连续运行测试
 */
void motor2_continuous_test(void)
{
    // 1. 系统初始化
    board_init();
    delay_ms(2000);
    
    // 2. 使能电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 3. 连续运行测试
    for(int cycle = 0; cycle < 3; cycle++)
    {
        // 顺时针加速：300->800->1200RPM
        Emm_V5_Vel_Control_USART(2, 1, 0, 300, 5, false);
        delay_ms(2000);
        
        Emm_V5_Vel_Control_USART(2, 1, 0, 800, 10, false);
        delay_ms(2000);
        
        Emm_V5_Vel_Control_USART(2, 1, 0, 1200, 15, false);
        delay_ms(2000);
        
        // 逆时针减速：1200->800->300RPM
        Emm_V5_Vel_Control_USART(2, 1, 1, 1200, 15, false);
        delay_ms(2000);
        
        Emm_V5_Vel_Control_USART(2, 1, 1, 800, 10, false);
        delay_ms(2000);
        
        Emm_V5_Vel_Control_USART(2, 1, 1, 300, 5, false);
        delay_ms(2000);
        
        // 短暂停止
        Emm_V5_Stop_Now_USART(2, 1, false);
        delay_ms(1000);
    }
    
    // 4. 禁用电机
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 主函数 - 选择测试程序
 */
int main(void)
{
    // 选择要运行的测试程序（取消注释对应的函数）
    
    motor2_basic_test();           // 基础测试（推荐先运行这个）
    // dual_motor_compare_test();     // 双电机对比测试
    // motor2_smooth_start_test();    // 渐进启动测试
    // motor2_continuous_test();      // 连续运行测试
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	故障排除指南
***
***	如果第二个电机仍然抖动不转，请检查：
***	1. 硬件连接：PA2->驱动器RX, PA3->驱动器TX
***	2. 电源供应：确保驱动器有足够的5V电源
***	3. 地线连接：STM32和驱动器必须共地
***	4. 驱动器设置：检查驱动器的细分和电流设置
***	5. 电机连接：确保电机正确连接到驱动器
***
***	调试步骤：
***	1. 先运行motor2_basic_test()验证基本功能
***	2. 使用示波器检查PA2的TX信号
***	3. 检查驱动器的LED指示灯状态
***	4. 尝试不同的速度和加速度参数
**********************************************************/
