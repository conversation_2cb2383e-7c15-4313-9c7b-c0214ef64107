/**********************************************************
***	第二个电机控制实例代码
***	硬件连接：STM32F103 + 两个Emm_V5.0驱动器
***	电机1：USART1 (PA9-TX, PA10-RX)
***	电机2：USART2 (PA2-TX, PA3-RX)
**********************************************************/

#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**
 * @brief 第二个电机基本速度控制示例
 */
void motor2_speed_control_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);  // 等待驱动器上电初始化
    
    // 使能第二个电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);   // 等待使能完成
    
    // 第二个电机速度控制演示
    
    // 1. 顺时针1000RPM，加速度10
    Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);
    delay_ms(3000);  // 运行3秒
    
    // 2. 逆时针800RPM，加速度15
    Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
    delay_ms(3000);  // 运行3秒
    
    // 3. 高速运行：顺时针2000RPM，加速度20
    Emm_V5_Vel_Control_USART(2, 1, 0, 2000, 20, false);
    delay_ms(2000);  // 运行2秒
    
    // 4. 低速运行：逆时针300RPM，加速度5
    Emm_V5_Vel_Control_USART(2, 1, 1, 300, 5, false);
    delay_ms(4000);  // 运行4秒
    
    // 5. 停止电机
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 6. 禁用电机
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 双电机对比运行示例
 */
void dual_motor_compare_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);           // 电机1 (USART1)
    Emm_V5_En_Control_USART(2, 1, true, false);  // 电机2 (USART2)
    delay_ms(100);
    
    // 对比测试：相同参数
    // 电机1：USART1控制，地址1，顺时针1000RPM
    Emm_V5_Vel_Control(1, 0, 1000, 10, false);
    // 电机2：USART2控制，地址1，顺时针1000RPM
    Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);
    delay_ms(5000);  // 同时运行5秒
    
    // 对比测试：不同方向
    // 电机1：顺时针
    Emm_V5_Vel_Control(1, 0, 800, 15, false);
    // 电机2：逆时针
    Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
    delay_ms(3000);
    
    // 对比测试：不同速度
    // 电机1：1500RPM
    Emm_V5_Vel_Control(1, 0, 1500, 10, false);
    // 电机2：500RPM
    Emm_V5_Vel_Control_USART(2, 1, 0, 500, 10, false);
    delay_ms(4000);
    
    // 同时停止
    Emm_V5_Stop_Now(1, false);
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    
    // 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 第二个电机同步控制示例
 */
void motor2_sync_control_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置同步运动参数（注意：snF参数设为true）
    // 电机1：顺时针1000RPM，同步标志=true
    Emm_V5_Vel_Control(1, 0, 1000, 10, true);
    // 电机2：逆时针1000RPM，同步标志=true
    Emm_V5_Vel_Control_USART(2, 1, 1, 1000, 10, true);
    
    delay_ms(500);  // 短暂延时确保命令发送完成
    
    // 触发同步运动（两个电机同时启动）
    Emm_V5_Synchronous_motion(1);  // 可以用任一电机地址触发
    delay_ms(5000);  // 同步运行5秒
    
    // 设置同步停止
    Emm_V5_Stop_Now(1, true);           // 电机1，同步标志=true
    Emm_V5_Stop_Now_USART(2, 1, true);  // 电机2，同步标志=true
    
    delay_ms(100);
    
    // 触发同步停止
    Emm_V5_Synchronous_motion(1);
    delay_ms(1000);
    
    // 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 第二个电机速度渐变示例
 */
void motor2_speed_ramp_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 使能第二个电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 速度递增测试：从200RPM到2000RPM
    for(uint16_t speed = 200; speed <= 2000; speed += 200)
    {
        // 第二个电机顺时针运行
        Emm_V5_Vel_Control_USART(2, 1, 0, speed, 8, false);
        delay_ms(2000);  // 每个速度运行2秒
    }
    
    delay_ms(1000);
    
    // 速度递减测试：从2000RPM到200RPM
    for(uint16_t speed = 2000; speed >= 200; speed -= 200)
    {
        // 第二个电机逆时针运行
        Emm_V5_Vel_Control_USART(2, 1, 1, speed, 8, false);
        delay_ms(1500);  // 每个速度运行1.5秒
    }
    
    // 停止并禁用
    Emm_V5_Stop_Now_USART(2, 1, false);
    delay_ms(1000);
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 第二个电机交替启停示例
 */
void motor2_start_stop_example(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 使能第二个电机
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 交替启停测试
    for(int i = 0; i < 5; i++)
    {
        // 启动：顺时针1000RPM
        Emm_V5_Vel_Control_USART(2, 1, 0, 1000, 10, false);
        delay_ms(2000);  // 运行2秒
        
        // 停止
        Emm_V5_Stop_Now_USART(2, 1, false);
        delay_ms(1000);  // 停止1秒
        
        // 启动：逆时针800RPM
        Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, false);
        delay_ms(1500);  // 运行1.5秒
        
        // 停止
        Emm_V5_Stop_Now_USART(2, 1, false);
        delay_ms(500);   // 停止0.5秒
    }
    
    // 禁用电机
    Emm_V5_En_Control_USART(2, 1, false, false);
}

/**
 * @brief 主函数 - 选择要运行的示例
 */
int main(void)
{
    // 选择要运行的示例（取消注释对应的函数）
    
    // motor2_speed_control_example();     // 基本速度控制
    // dual_motor_compare_example();       // 双电机对比
    // motor2_sync_control_example();      // 同步控制
    // motor2_speed_ramp_example();        // 速度渐变
    motor2_start_stop_example();           // 交替启停
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}
