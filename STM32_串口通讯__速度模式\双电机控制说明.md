# STM32双电机串口控制系统

## 概述
本项目已扩展为支持同时控制两个Emm_V5.0步进电机驱动器，通过STM32的USART1和USART2分别控制两个电机。

## 硬件连接

### STM32引脚分配
```
USART1 (控制电机1):
- PA9  -> USART1_TX (连接到电机1驱动器的RX)
- PA10 -> USART1_RX (连接到电机1驱动器的TX)

USART2 (控制电机2):
- PA2  -> USART2_TX (连接到电机2驱动器的RX)  
- PA3  -> USART2_RX (连接到电机2驱动器的TX)
```

### 电机驱动器连接
```
电机1驱动器:
- RX -> STM32 PA9 (USART1_TX)
- TX -> STM32 PA10 (USART1_RX)
- GND -> STM32 GND
- VCC -> 5V电源

电机2驱动器:
- RX -> STM32 PA2 (USART2_TX)
- TX -> STM32 PA3 (USART2_RX)
- GND -> STM32 GND
- VCC -> 5V电源
```

## 软件架构

### 主要修改
1. **FIFO系统**: 支持双串口独立缓冲区
2. **串口驱动**: 添加USART2支持和中断处理
3. **电机控制**: 新增指定串口的控制函数
4. **向后兼容**: 保留原有函数接口

### 新增函数接口
```c
// 指定串口的控制函数 (usart_num: 1=USART1, 2=USART2)
void Emm_V5_Vel_Control_USART(uint8_t usart_num, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF);
void Emm_V5_En_Control_USART(uint8_t usart_num, uint8_t addr, bool state, bool snF);
void Emm_V5_Stop_Now_USART(uint8_t usart_num, uint8_t addr, bool snF);
void Emm_V5_Reset_CurPos_To_Zero_USART(uint8_t usart_num, uint8_t addr);
void Emm_V5_Reset_Clog_Pro_USART(uint8_t usart_num, uint8_t addr);
```

## 使用示例

### 基本双电机控制
```c
// 电机1 (USART1) - 顺时针1000RPM
Emm_V5_Vel_Control(1, 0, 1000, 10, 0);

// 电机2 (USART2) - 逆时针800RPM  
Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, 0);
```

### 电机使能控制
```c
// 使能电机1
Emm_V5_En_Control(1, true, false);

// 使能电机2
Emm_V5_En_Control_USART(2, 1, true, false);
```

### 停止电机
```c
// 停止电机1
Emm_V5_Stop_Now(1, false);

// 停止电机2
Emm_V5_Stop_Now_USART(2, 1, false);
```

## 配置参数

### 串口配置
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 流控制: 无

### 电机地址设置
- 电机1地址: 1 (默认)
- 电机2地址: 1 (可根据需要修改)

## 注意事项

1. **电源供电**: 确保电机驱动器有足够的电源供应
2. **地线连接**: 所有设备必须共地
3. **地址冲突**: 如果使用相同地址，确保在不同串口上
4. **中断优先级**: USART1优先级为0，USART2优先级为1
5. **兼容性**: 原有单电机代码无需修改，自动使用USART1

## 故障排除

### 常见问题
1. **电机无响应**: 检查串口连接和波特率设置
2. **只有一个电机工作**: 检查USART2初始化和引脚配置
3. **通信错误**: 确认地线连接和电源稳定性

### 调试方法
1. 使用示波器检查TX/RX信号
2. 检查串口中断是否正常触发
3. 验证FIFO缓冲区数据接收

## 完整连接示意图

```
STM32F103                    电机驱动器1 (Emm_V5.0)
┌─────────────┐             ┌─────────────────────┐
│    PA9(TX1) │────────────→│ RX                  │
│    PA10(RX1)│←────────────│ TX                  │
│             │             │                     │
│    PA2(TX2) │─────────┐   │ GND ────────────────┼─── GND
│    PA3(RX2) │─────────┼─┐ │ VCC ────────────────┼─── +5V
│             │         │ │ └─────────────────────┘
│        GND  │─────────┼─┼─┐
│        VCC  │─────────┼─┼─┼─── +5V
└─────────────┘         │ │ │
                        │ │ │  电机驱动器2 (Emm_V5.0)
                        │ │ │ ┌─────────────────────┐
                        │ │ └→│ GND                 │
                        │ └──→│ VCC                 │
                        └────→│ RX                  │
                         ┌───→│ TX                  │
                         │    └─────────────────────┘
                         │
                    STM32 PA3(RX2)
```

## 代码结构说明

### 文件修改清单
- `DRIVERS/fifo.h` - 支持双FIFO缓冲区
- `DRIVERS/fifo.c` - 实现独立的FIFO操作
- `BSP/usart.h` - 添加USART2函数声明
- `BSP/usart.c` - 实现USART2中断和发送函数
- `BSP/board.c` - 添加USART2初始化
- `BSP/Emm_V5.h` - 添加指定串口的函数声明
- `BSP/Emm_V5.c` - 实现双串口控制函数
- `APP/main.c` - 双电机控制示例

### 主循环示例
当前main.c实现了一个演示程序：
1. 初始化后两个电机开始以不同速度旋转
2. 每5秒切换一次旋转方向和速度
3. 展示了完整的启动、停止、方向切换流程

## 扩展功能

系统支持进一步扩展：
- 可添加更多串口支持更多电机
- 支持不同波特率配置
- 可实现电机同步控制
- 支持位置模式和其他控制模式
- 可添加电机状态反馈和监控

## 配置系统

### 统一配置管理
新增了`motor_config.h`和`motor_config.c`文件，提供统一的配置管理：

```c
#include "motor_config.h"

// 使用便捷宏控制电机
DUAL_MOTOR_ENABLE();                    // 使能双电机
MOTOR1_VEL_CONTROL(MOTOR_DIR_CW, 1000, 10, false); // 电机1控制
MOTOR2_VEL_CONTROL(MOTOR_DIR_CCW, 800, 15, false); // 电机2控制
DUAL_MOTOR_STOP();                      // 停止双电机
```

### 高级控制函数
```c
// 带配置的速度控制（0表示使用默认值）
motor_vel_control_ex(1, MOTOR_DIR_CW, 0, 0, false); // 使用默认速度和加速度

// 带配置的位置控制
motor_pos_control_ex(2, MOTOR_DIR_CCW, 500, 10, 3200, false, false);

// 紧急停止
motor_emergency_stop();
```

### 配置参数
所有重要参数都在`motor_config.h`中定义：
- 电机地址和串口分配
- 默认速度和加速度
- 安全限制参数
- 通信超时设置

## 文件结构

```
STM32_串口通讯__速度模式/
├── APP/
│   ├── main.c                    # 主程序（已修改为双电机示例）
│   └── stm32f10x_conf.h
├── BSP/
│   ├── board.c/h                 # 系统初始化（已添加USART2支持）
│   ├── usart.c/h                 # 串口驱动（已扩展双串口）
│   ├── Emm_V5.c/h               # 电机控制库（已添加双串口函数）
│   ├── motor_config.c/h          # 配置管理系统（新增）
├── DRIVERS/
│   ├── fifo.c/h                  # FIFO缓冲（已修改为双缓冲）
│   └── delay.c/h
├── 双电机控制说明.md              # 本说明文件
├── 双电机控制示例.c               # 详细使用示例
└── ...
```

## 技术特点

1. **模块化设计**: 每个串口独立管理，互不干扰
2. **向后兼容**: 原有代码无需修改
3. **中断驱动**: 高效的串口数据处理
4. **FIFO缓冲**: 防止数据丢失
5. **灵活配置**: 支持不同电机地址和参数
6. **统一管理**: 新增配置系统，便于参数管理
7. **安全保护**: 内置速度和加速度限制检查
8. **状态监控**: 实时跟踪电机运行状态
