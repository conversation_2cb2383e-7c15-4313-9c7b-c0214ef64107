#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	命令触发回零示例程序
***	功能：通过串口命令触发电机回零
***	命令格式：
***	0x01 0x00 - 电机1回零
***	0x02 0x00 - 电机2回零
***	0x03 0x00 - 双电机同步回零
***	0x04 0x00 - 重新设置零点
**********************************************************/

// 命令定义
#define CMD_MOTOR1_RETURN_ZERO    0x01
#define CMD_MOTOR2_RETURN_ZERO    0x02
#define CMD_DUAL_RETURN_ZERO      0x03
#define CMD_RESET_ZERO_POINT      0x04

// 回零参数
#define RETURN_SPEED              800     // 回零速度 (RPM)
#define RETURN_ACCELERATION       10      // 回零加速度

/**
 * @brief 初始化并设置零点
 */
void init_zero_point(void)
{
    // 系统初始化
    board_init();
    delay_ms(2000);
    
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置当前位置为零点
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    delay_ms(100);
    
    // 设置原点并保存
    Emm_V5_Origin_Set_O(1, true);
    Emm_V5_Origin_Set_O_USART(2, 1, true);
    delay_ms(100);
}

/**
 * @brief 电机1回零
 */
void motor1_return_zero(void)
{
    // 确保电机使能
    Emm_V5_En_Control(1, true, false);
    delay_ms(100);
    
    // 绝对位置控制回到零点
    Emm_V5_Pos_Control(1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 电机2回零
 */
void motor2_return_zero(void)
{
    // 确保电机使能
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 绝对位置控制回到零点
    Emm_V5_Pos_Control_USART(2, 1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, false);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 双电机同步回零
 */
void dual_motor_return_zero(void)
{
    // 确保两个电机都使能
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 设置同步回零参数
    Emm_V5_Pos_Control(1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, true);
    Emm_V5_Pos_Control_USART(2, 1, 0, RETURN_SPEED, RETURN_ACCELERATION, 0, true, true);
    delay_ms(100);
    
    // 触发同步运动
    Emm_V5_Synchronous_motion(1);
    delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 重新设置零点
 */
void reset_zero_point(void)
{
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    Emm_V5_En_Control_USART(2, 1, true, false);
    delay_ms(100);
    
    // 复位当前位置为零点
    Emm_V5_Reset_CurPos_To_Zero(1);
    Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
    delay_ms(100);
    
    // 设置原点并保存
    Emm_V5_Origin_Set_O(1, true);
    Emm_V5_Origin_Set_O_USART(2, 1, true);
    delay_ms(100);
}

/**
 * @brief 处理回零命令
 */
void process_return_zero_commands(void)
{
    // 检查USART1接收到的命令
    if(rxFrameFlag) {
        rxFrameFlag = false;
        
        // 检查命令长度
        if(rxCount >= 2) {
            uint8_t cmd = rxCmd[0];
            uint8_t param = rxCmd[1];
            
            // 处理命令
            switch(cmd) {
                case CMD_MOTOR1_RETURN_ZERO:
                    if(param == 0x00) {
                        motor1_return_zero();
                    }
                    break;
                    
                case CMD_MOTOR2_RETURN_ZERO:
                    if(param == 0x00) {
                        motor2_return_zero();
                    }
                    break;
                    
                case CMD_DUAL_RETURN_ZERO:
                    if(param == 0x00) {
                        dual_motor_return_zero();
                    }
                    break;
                    
                case CMD_RESET_ZERO_POINT:
                    if(param == 0x00) {
                        reset_zero_point();
                    }
                    break;
                    
                default:
                    // 未知命令，忽略
                    break;
            }
        }
    }
    
    // 也可以检查USART2的命令（如果需要）
    if(rxFrameFlag2) {
        rxFrameFlag2 = false;
        // 处理USART2的命令...
    }
}

/**
 * @brief 演示电机运动和回零
 */
void demo_motion_and_return(void)
{
    // 演示电机运动
    // 电机1移动到180°位置
    Emm_V5_Pos_Control(1, 0, 1000, 15, 1600, false, false);
    delay_ms(1200);
    
    // 电机2移动到270°位置
    Emm_V5_Pos_Control_USART(2, 1, 0, 1000, 15, 2400, false, false);
    delay_ms(1500);
    
    delay_ms(2000);  // 停留2秒
    
    // 演示回零功能
    dual_motor_return_zero();
    delay_ms(1000);
}

/**
 * @brief 主函数
 */
int main(void)
{
    // 初始化并设置零点
    init_zero_point();
    
    // 运行一次演示
    demo_motion_and_return();
    
    // 主循环：等待并处理回零命令
    while(1)
    {
        // 处理回零命令
        process_return_zero_commands();
        
        // 其他任务处理...
        
        delay_ms(10);  // 短暂延时，避免CPU占用过高
    }
}

/**********************************************************
***	使用说明：
***
***	1. 命令格式：
***	   - 0x01 0x00: 电机1回零
***	   - 0x02 0x00: 电机2回零
***	   - 0x03 0x00: 双电机同步回零
***	   - 0x04 0x00: 重新设置零点
***
***	2. 发送方式：
***	   通过串口调试助手或上位机软件发送十六进制命令
***	   例如：发送 "01 00" 触发电机1回零
***
***	3. 回零逻辑：
***	   - 使用绝对位置控制方式
***	   - 目标位置设为0（零点）
***	   - 回零速度800RPM，加速度10
***	   - 支持单独回零和同步回零
***
***	4. 零点设置：
***	   - 上电时自动将当前位置设为零点
***	   - 可通过命令重新设置零点
***	   - 零点信息保存到驱动器Flash中
***
***	5. 扩展功能：
***	   - 可添加回零状态反馈
***	   - 可添加回零完成确认
***	   - 可添加回零失败处理
***	   - 可添加不同速度的回零模式
**********************************************************/
