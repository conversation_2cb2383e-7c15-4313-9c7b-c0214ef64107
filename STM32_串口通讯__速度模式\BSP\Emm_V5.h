#ifndef __EMM_V5_H
#define __EMM_V5_H

#include "usart.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：支持USART1和USART2同时控制两个电机
**********************************************************/

#define		ABS(x)		((x) > 0 ? (x) : -(x)) 

typedef enum {
	S_VER   = 0,			/* 获取固件版本与对应的硬件版本 */
	S_RL    = 1,			/* 获取读取或设置电机转向 */
	S_PID   = 2,			/* 获取PID参数 */
	S_VBUS  = 3,			/* 获取总线电压 */
	S_CPHA  = 5,			/* 获取相电流 */
	S_ENCL  = 7,			/* 获取经过校准或校准后的编码器值 */
	S_TPOS  = 8,			/* 获取电机目标位置角度 */
	S_VEL   = 9,			/* 获取电机实时转速 */
	S_CPOS  = 10,			/* 获取电机实时位置角度 */
	S_PERR  = 11,			/* 获取电机位置误差角度 */
	S_FLAG  = 13,			/* 获取使能/到位/堵转状态标志位 */
	S_Conf  = 14,			/* 获取配置参数 */
	S_State = 15,			/* 获取系统状态参数 */
	S_ORG   = 16,     /* 获取回零/限位失能状态标志位 */
}SysParams_t;


/**********************************************************
*** ע�⣺ÿ�������Ĳ����ľ���˵��������Ķ�Ӧ������ע��˵��
**********************************************************/
void Emm_V5_Reset_CurPos_To_Zero(uint8_t addr); // ����ǰλ������
void Emm_V5_Reset_Clog_Pro(uint8_t addr); // �����ת����
void Emm_V5_Read_Sys_Params(uint8_t addr, SysParams_t s); // ��ȡ����
void Emm_V5_Modify_Ctrl_Mode(uint8_t addr, bool svF, uint8_t ctrl_mode); // ���������޸Ŀ���/�ջ�����ģʽ
void Emm_V5_En_Control(uint8_t addr, bool state, bool snF); // ���ʹ�ܿ���
void Emm_V5_Vel_Control(uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF); // �ٶ�ģʽ����
void Emm_V5_Pos_Control(uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF); // λ��ģʽ����
void Emm_V5_Stop_Now(uint8_t addr, bool snF); // �õ������ֹͣ�˶�
void Emm_V5_Synchronous_motion(uint8_t addr); // �������ͬ����ʼ�˶�
void Emm_V5_Origin_Set_O(uint8_t addr, bool svF); // ���õ�Ȧ��������λ��
void Emm_V5_Origin_Modify_Params(uint8_t addr, bool svF, uint8_t o_mode, uint8_t o_dir, uint16_t o_vel, uint32_t o_tm, uint16_t sl_vel, uint16_t sl_ma, uint16_t sl_ms, bool potF); // �޸Ļ������
void Emm_V5_Origin_Trigger_Return(uint8_t addr, uint8_t o_mode, bool snF); // �������������
void Emm_V5_Origin_Interrupt(uint8_t addr); // ǿ���жϲ��˳�����

// ָ�����ڵĺ���
void Emm_V5_Reset_CurPos_To_Zero_USART(uint8_t usart_num, uint8_t addr); // ����ǰλ������
void Emm_V5_Reset_Clog_Pro_USART(uint8_t usart_num, uint8_t addr); // �����ת����
void Emm_V5_Read_Sys_Params_USART(uint8_t usart_num, uint8_t addr, SysParams_t s); // ��ȡ����
void Emm_V5_Modify_Ctrl_Mode_USART(uint8_t usart_num, uint8_t addr, bool svF, uint8_t ctrl_mode); // ���������޸Ŀ���/�ջ�����ģʽ
void Emm_V5_En_Control_USART(uint8_t usart_num, uint8_t addr, bool state, bool snF); // ���ʹ�ܿ���
void Emm_V5_Vel_Control_USART(uint8_t usart_num, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF); // �ٶ�ģʽ����
void Emm_V5_Pos_Control_USART(uint8_t usart_num, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF); // λ��ģʽ����
void Emm_V5_Stop_Now_USART(uint8_t usart_num, uint8_t addr, bool snF); // �õ������ֹͣ�˶�
void Emm_V5_Synchronous_motion_USART(uint8_t usart_num, uint8_t addr); // �������ͬ����ʼ�˶�
void Emm_V5_Origin_Set_O_USART(uint8_t usart_num, uint8_t addr, bool svF); // ���õ�Ȧ��������λ��
void Emm_V5_Origin_Modify_Params_USART(uint8_t usart_num, uint8_t addr, bool svF, uint8_t o_mode, uint8_t o_dir, uint16_t o_vel, uint32_t o_tm, uint16_t sl_vel, uint16_t sl_ma, uint16_t sl_ms, bool potF); // �޸Ļ������
void Emm_V5_Origin_Trigger_Return_USART(uint8_t usart_num, uint8_t addr, uint8_t o_mode, bool snF); // �������������
void Emm_V5_Origin_Interrupt_USART(uint8_t usart_num, uint8_t addr); // ǿ���жϲ��˳�����

#endif
