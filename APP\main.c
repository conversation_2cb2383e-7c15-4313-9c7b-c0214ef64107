#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库 - 双电机控制示例
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：同时控制两个步进电机
**********************************************************/

/**********************************************************
***	回零命令函数定义（最终简化版，只保留一个有效方法）
**********************************************************/

// 电机2地址设置（根据实际情况修改）
#define MOTOR2_ADDR 2  // 如果电机2不动，尝试改为1

/**
 * @brief 双电机回零命令（简化版，统一地址）
 * 这是唯一保留的回零方法
 */
void dual_motor_return_to_zero(void)
{
	// 第一步：使能两个电机
	Emm_V5_En_Control(1, true, false);
	delay_ms(100);
	Emm_V5_En_Control_USART(2, MOTOR2_ADDR, true, false);
	delay_ms(200);

	// 第二步：复位位置到零点
	Emm_V5_Reset_CurPos_To_Zero(1);
	delay_ms(100);
	Emm_V5_Reset_CurPos_To_Zero_USART(2, MOTOR2_ADDR);
	delay_ms(200);

	// 第三步：分别回零
	// 电机1回零
	Emm_V5_Pos_Control(1, 0, 800, 10, 0, true, false);
	delay_ms(100);

	// 电机2回零
	Emm_V5_Pos_Control_USART(2, MOTOR2_ADDR, 0, 800, 10, 0, true, false);

	// 等待回零完成
	delay_ms(2000);
}

/**
	*	@brief		主函数 - 双电机位置控制演示（带零点设置和回零功能）
	*	@param		无
	*	@retval		无
	*/
int main(void)
{
/**********************************************************
***	初始化系统硬件
**********************************************************/
	board_init();

/**********************************************************
***	上电延时2秒等待Emm_V5.0驱动器初始化完成
**********************************************************/
	delay_ms(2000);

/**********************************************************
***	基本初始化（简化版）
**********************************************************/
	// 基本使能
	Emm_V5_En_Control(1, true, false);
	Emm_V5_En_Control_USART(2, MOTOR2_ADDR, true, false);  // 使用统一地址
	delay_ms(500);

	// 基本复位
	Emm_V5_Reset_CurPos_To_Zero(1);
	Emm_V5_Reset_CurPos_To_Zero_USART(2, MOTOR2_ADDR);     // 使用统一地址
	delay_ms(500);

/**********************************************************
***	双电机位置模式控制 - 180°每秒旋转
***	电机1(USART1): 顺时针180°，速度1600RPM，1600脉冲
***	电机2(USART2): 逆时针180°，速度1600RPM，1600脉冲
***	计算公式：180° = 1600脉冲，180°/秒 = 1600RPM
**********************************************************/
  // 电机1 - 顺时针180°每秒
  Emm_V5_Pos_Control(1, 0, 1600, 15, 400, false, false);
	

  // 电机2 - 逆时针180°每秒
  Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 400, false, false);
delay_ms(500);
	dual_motor_return_to_zero();
	
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	WHILEѭ�� - ˫������ʾ
**********************************************************/
	while(1)
	{

	}
}
