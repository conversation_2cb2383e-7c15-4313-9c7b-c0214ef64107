#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库 - 双电机控制示例
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：同时控制两个步进电机
**********************************************************/

/**********************************************************
***	回零命令函数定义（最终简化版，只保留一个有效方法）
**********************************************************/

// 电机2可能的地址列表
static uint8_t motor2_addresses[] = {1, 2, 3, 4, 5};

/**
 * @brief 双电机同步回零命令（解决电机2同步问题的终极版本）
 * 这是唯一保留的回零方法，解决所有电机2问题
 */
void dual_motor_return_to_zero(void)
{
	// 第一步：强制使能电机1
	Emm_V5_En_Control(1, true, false);
	delay_ms(200);

	// 第二步：强制使能电机2（尝试所有可能地址）
	for(int i = 0; i < 5; i++)
	{
		uint8_t addr = motor2_addresses[i];
		Emm_V5_En_Control_USART(2, addr, true, false);
		delay_ms(100);  // 增加延时确保使能成功
	}
	delay_ms(300);  // 额外延时确保所有使能完成

	// 第三步：重新复位位置（确保零点正确）
	Emm_V5_Reset_CurPos_To_Zero(1);
	delay_ms(100);
	for(int i = 0; i < 5; i++)
	{
		uint8_t addr = motor2_addresses[i];
		Emm_V5_Reset_CurPos_To_Zero_USART(2, addr);
		delay_ms(100);
	}
	delay_ms(300);

	// 第四步：使用非同步方式分别回零（更可靠）
	// 先让电机1回零
	Emm_V5_Pos_Control(1, 0, 800, 10, 0, true, false);
	delay_ms(100);

	// 再让电机2回零（尝试所有地址）
	for(int i = 0; i < 5; i++)
	{
		uint8_t addr = motor2_addresses[i];
		Emm_V5_Pos_Control_USART(2, addr, 0, 800, 10, 0, true, false);
		delay_ms(100);
	}

	// 等待两个电机都回零完成
	delay_ms(3000);  // 增加等待时间确保回零完成
}

/**
	*	@brief		主函数 - 双电机位置控制演示（带零点设置和回零功能）
	*	@param		无
	*	@retval		无
	*/
int main(void)
{
/**********************************************************
***	初始化系统硬件
**********************************************************/
	board_init();

/**********************************************************
***	上电延时2秒等待Emm_V5.0驱动器初始化完成
**********************************************************/
	delay_ms(2000);

/**********************************************************
***	基本初始化（简化版）
**********************************************************/
	// 基本使能（回零函数会重新处理）
	Emm_V5_En_Control(1, true, false);
	Emm_V5_En_Control_USART(2, 1, true, false);
	Emm_V5_En_Control_USART(2, 2, true, false);
	delay_ms(500);

	// 基本复位（回零函数会重新处理）
	Emm_V5_Reset_CurPos_To_Zero(1);
	Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
	Emm_V5_Reset_CurPos_To_Zero_USART(2, 2);
	delay_ms(500);

/**********************************************************
***	双电机位置模式控制 - 180°每秒旋转
***	电机1(USART1): 顺时针180°，速度1600RPM，1600脉冲
***	电机2(USART2): 逆时针180°，速度1600RPM，1600脉冲
***	计算公式：180° = 1600脉冲，180°/秒 = 1600RPM
**********************************************************/
  // 电机1 - 顺时针180°每秒
  Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);
	

  // 电机2 - 逆时针180°每秒（简化版）
  Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);
  Emm_V5_Pos_Control_USART(2, 2, 1, 1600, 15, 1600, false, false);

	
	
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	WHILEѭ�� - ˫������ʾ
**********************************************************/
	while(1)
	{
		// 等待第一次运动完成后，间隔2秒进行下一次运动
		delay_ms(2000);

//		// 第二次位置运动：继续相同方向180°
//		Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);        // 电机1顺时针180°
//		Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false); // 电机2逆时针180°
//		delay_ms(1200);  // 等待运动完成

//		delay_ms(2000);  // 间隔2秒

		// 第三次位置运动：切换方向180°
		Emm_V5_Pos_Control(1, 1, 1600, 15, 1600, false, false);        // 电机1逆时针180°
		Emm_V5_Pos_Control_USART(2, 1, 0, 1600, 15, 1600, false, false); // 电机2顺时针180°
		Emm_V5_Pos_Control_USART(2, 2, 0, 1600, 15, 1600, false, false); // 电机2顺时针180°
		delay_ms(1200);  // 等待运动完成

//		delay_ms(2000);  // 间隔2秒

//		// 第四次位置运动：回到初始方向180°
//		Emm_V5_Pos_Control(1, 0, 1600, 15, 400, false, false);        // 电机1顺时针180°
//		Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 3200, false, false); // 电机2逆时针180°
//		delay_ms(1200);  // 等待运动完成

//		delay_ms(3000);  // 间隔3秒

//		// 演示回零功能：双电机同步回到零点位置
		dual_motor_return_to_zero();
		delay_ms(2000);  // 在零点停留2秒
	}
}
