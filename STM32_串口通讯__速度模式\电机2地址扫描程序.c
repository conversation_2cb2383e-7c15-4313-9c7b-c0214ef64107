#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	电机2地址扫描程序
***	功能：自动扫描电机2可能的地址，找到正确的地址设置
***	原理：尝试不同地址的使能和移动命令，观察电机响应
**********************************************************/

/**
 * @brief 扫描电机2的正确地址
 */
void scan_motor2_address(void)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 扫描地址1到10
    for(uint8_t addr = 1; addr <= 10; addr++)
    {
        // 尝试使能当前地址的电机
        Emm_V5_En_Control_USART(2, addr, true, false);
        delay_ms(200);
        
        // 尝试复位位置
        Emm_V5_Reset_CurPos_To_Zero_USART(2, addr);
        delay_ms(200);
        
        // 尝试小幅移动（200脉冲 ≈ 22.5°）
        Emm_V5_Pos_Control_USART(2, addr, 0, 300, 5, 200, false, false);
        delay_ms(1500);  // 等待移动完成
        
        // 尝试回零
        Emm_V5_Pos_Control_USART(2, addr, 0, 300, 5, 0, true, false);
        delay_ms(1500);  // 等待回零完成
        
        // 禁用电机
        Emm_V5_En_Control_USART(2, addr, false, false);
        delay_ms(500);
        
        // 如果电机有响应，这个地址就是正确的
        // 可以通过观察电机运动来判断
        delay_ms(1000);  // 间隔1秒再测试下一个地址
    }
}

/**
 * @brief 测试特定地址的电机功能
 */
void test_specific_address(uint8_t addr)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能电机
    Emm_V5_En_Control_USART(2, addr, true, false);
    delay_ms(200);
    
    // 复位位置
    Emm_V5_Reset_CurPos_To_Zero_USART(2, addr);
    delay_ms(200);
    
    // 测试不同角度的移动
    uint16_t test_angles[] = {400, 800, 1600, 2400};  // 45°, 90°, 180°, 270°
    
    for(int i = 0; i < 4; i++)
    {
        // 移动到指定角度
        Emm_V5_Pos_Control_USART(2, addr, 0, 500, 10, test_angles[i], false, false);
        delay_ms(2000);  // 等待移动完成
        
        // 回零
        Emm_V5_Pos_Control_USART(2, addr, 0, 500, 10, 0, true, false);
        delay_ms(2000);  // 等待回零完成
        
        delay_ms(1000);  // 间隔1秒
    }
    
    // 禁用电机
    Emm_V5_En_Control_USART(2, addr, false, false);
}

/**
 * @brief 对比测试：电机1 vs 电机2
 */
void compare_motor1_vs_motor2(uint8_t motor2_addr)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 使能两个电机
    Emm_V5_En_Control(1, true, false);                    // 电机1
    Emm_V5_En_Control_USART(2, motor2_addr, true, false); // 电机2
    delay_ms(200);
    
    // 复位两个电机
    Emm_V5_Reset_CurPos_To_Zero(1);                       // 电机1
    Emm_V5_Reset_CurPos_To_Zero_USART(2, motor2_addr);    // 电机2
    delay_ms(200);
    
    // 同步测试
    for(int cycle = 0; cycle < 3; cycle++)
    {
        // 同时移动180°
        Emm_V5_Pos_Control(1, 0, 800, 10, 1600, false, false);                    // 电机1
        Emm_V5_Pos_Control_USART(2, motor2_addr, 1, 800, 10, 1600, false, false); // 电机2反向
        delay_ms(2500);  // 等待移动完成
        
        // 同时回零
        Emm_V5_Pos_Control(1, 0, 800, 10, 0, true, false);                    // 电机1
        Emm_V5_Pos_Control_USART(2, motor2_addr, 0, 800, 10, 0, true, false); // 电机2
        delay_ms(2500);  // 等待回零完成
        
        delay_ms(1000);  // 循环间隔
    }
    
    // 禁用电机
    Emm_V5_En_Control(1, false, false);
    Emm_V5_En_Control_USART(2, motor2_addr, false, false);
}

/**
 * @brief 详细的单地址测试
 */
void detailed_single_address_test(uint8_t addr)
{
    // 初始化系统
    board_init();
    delay_ms(2000);
    
    // 测试使能功能
    for(int i = 0; i < 3; i++)
    {
        Emm_V5_En_Control_USART(2, addr, true, false);   // 使能
        delay_ms(500);
        Emm_V5_En_Control_USART(2, addr, false, false);  // 禁用
        delay_ms(500);
    }
    
    // 使能电机进行后续测试
    Emm_V5_En_Control_USART(2, addr, true, false);
    delay_ms(200);
    
    // 测试复位功能
    for(int i = 0; i < 3; i++)
    {
        Emm_V5_Reset_CurPos_To_Zero_USART(2, addr);
        delay_ms(300);
        
        Emm_V5_Origin_Set_O_USART(2, addr, false);  // 设置原点（不保存）
        delay_ms(300);
    }
    
    // 测试位置控制
    uint16_t speeds[] = {200, 500, 800, 1200};
    uint32_t positions[] = {200, 400, 800, 1600};
    
    for(int i = 0; i < 4; i++)
    {
        // 移动到指定位置
        Emm_V5_Pos_Control_USART(2, addr, 0, speeds[i], 10, positions[i], false, false);
        delay_ms(2000);
        
        // 回零
        Emm_V5_Pos_Control_USART(2, addr, 0, speeds[i], 10, 0, true, false);
        delay_ms(2000);
        
        delay_ms(500);
    }
    
    // 禁用电机
    Emm_V5_En_Control_USART(2, addr, false, false);
}

/**
 * @brief 主函数 - 选择测试模式
 */
int main(void)
{
    // 选择要运行的测试（取消注释对应的测试）
    
    scan_motor2_address();                    // 自动扫描地址（推荐先运行）
    // test_specific_address(2);                // 测试特定地址（如地址2）
    // compare_motor1_vs_motor2(2);            // 对比测试（假设电机2地址为2）
    // detailed_single_address_test(2);        // 详细测试特定地址
    
    // 主循环
    while(1)
    {
        delay_ms(100);
    }
}

/**********************************************************
***	使用说明：
***
***	1. 地址扫描测试：
***	   运行 scan_motor2_address() 函数
***	   观察电机2在哪个地址下有响应
***	   记录下有效的地址
***
***	2. 特定地址测试：
***	   如果发现电机2在地址2有响应
***	   运行 test_specific_address(2) 进行详细测试
***
***	3. 对比测试：
***	   确定电机2地址后，运行对比测试
***	   验证两个电机是否都能正常工作
***
***	4. 观察方法：
***	   - 电机有响应：电机会转动
***	   - 电机无响应：电机保持静止
***	   - 注意观察电机的转动角度和方向
***
***	5. 常见地址：
***	   - 地址1：大多数驱动器的默认地址
***	   - 地址2：部分驱动器的出厂设置
***	   - 地址3-10：用户自定义地址
***
***	6. 解决方案：
***	   找到正确地址后，修改main.c中的地址参数
***	   或者调整驱动器的地址设置拨码开关
***
***	7. 硬件检查：
***	   如果所有地址都无响应，检查：
***	   - PA2/PA3连接是否正确
***	   - 电源供应是否正常
***	   - 驱动器是否正常工作（LED指示）
**********************************************************/
