#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库 - 双电机控制示例
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：同时控制两个步进电机
**********************************************************/

/**
	*	@brief		主函数 - 双电机控制演示
	*	@param		无
	*	@retval		无
	*/
int main(void)
{
/**********************************************************
***	��ʼ����������
**********************************************************/
	board_init();

/**********************************************************
***	�ϵ���ʱ2��ȴ�Emm_V5.0�ջ���ʼ�����
**********************************************************/	
	delay_ms(2000);

/**********************************************************
***	�ٶ�ģʽ������CW���ٶ�1000RPM�����ٶ�10
**********************************************************/	
  // ����1 - ʹ��USART1
  Emm_V5_Vel_Control(1, 0, 1000, 0, 0);

  // ����2 - ʹ��USART2
  Emm_V5_Vel_Control_USART(2, 0, 1, 800, 0, 0);
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	WHILEѭ�� - ˫������ʾ
**********************************************************/
	while(1)
	{
		// ÿ5���л�һ�ε��ת����
		delay_ms(5000);

		// ֹͣ������
		Emm_V5_Stop_Now(1, false);           // ֹͣ����1
		Emm_V5_Stop_Now_USART(2, 1, false);  // ֹͣ����2
		delay_ms(1000);

		// �л�����
		Emm_V5_Vel_Control(1, 1, 800, 10, 0);        // ����1��ʱ��800RPM
		Emm_V5_Vel_Control_USART(2, 1, 0, 1200, 15, 0); // ����2˳ʱ��1200RPM
		delay_ms(5000);

		// ֹͣ������
		Emm_V5_Stop_Now(1, false);
		Emm_V5_Stop_Now_USART(2, 1, false);
		delay_ms(1000);

		// �ָ���ʼ״̬
		Emm_V5_Vel_Control(1, 0, 1000, 10, 0);       // ����1˳ʱ��1000RPM
		Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, 0);  // ����2��ʱ��800RPM
	}
}
