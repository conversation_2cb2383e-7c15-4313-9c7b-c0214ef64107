#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库 - 双电机控制示例
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：同时控制两个步进电机
**********************************************************/

/**
	*	@brief		主函数 - 双电机控制演示
	*	@param		无
	*	@retval		无
	*/
int main(void)
{
/**********************************************************
***	初始化系统硬件
**********************************************************/
	board_init();

/**********************************************************
***	上电延时2秒等待Emm_V5.0驱动器初始化完成
**********************************************************/
	delay_ms(2000);

/**********************************************************
***	使能两个电机（关键步骤！）
**********************************************************/
	// 使能电机1 (USART1)
	Emm_V5_En_Control(1, true, false);
	// 使能电机2 (USART2)
	Emm_V5_En_Control_USART(2, 1, true, false);
	delay_ms(100);  // 等待使能完成

/**********************************************************
***	双电机位置模式控制 - 180°每秒旋转
***	电机1(USART1): 顺时针180°，速度1600RPM，1600脉冲
***	电机2(USART2): 逆时针180°，速度1600RPM，1600脉冲
***	计算公式：180° = 1600脉冲，180°/秒 = 1600RPM
**********************************************************/
  // 电机1 - 顺时针180°每秒
  Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);

  // 电机2 - 逆时针180°每秒
  Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	WHILEѭ�� - ˫������ʾ
**********************************************************/
	while(1)
	{
		// 等待第一次运动完成后，间隔2秒进行下一次运动
		delay_ms(2000);

		// 第二次位置运动：继续相同方向180°
		Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);        // 电机1顺时针180°
		Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false); // 电机2逆时针180°
		delay_ms(1200);  // 等待运动完成

		delay_ms(2000);  // 间隔2秒

		// 第三次位置运动：切换方向180°
		Emm_V5_Pos_Control(1, 1, 1600, 15, 1600, false, false);        // 电机1逆时针180°
		Emm_V5_Pos_Control_USART(2, 1, 0, 1600, 15, 1600, false, false); // 电机2顺时针180°
		delay_ms(1200);  // 等待运动完成

		delay_ms(2000);  // 间隔2秒

		// 第四次位置运动：回到初始方向180°
		Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);        // 电机1顺时针180°
		Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false); // 电机2逆时针180°
		delay_ms(1200);  // 等待运动完成
	}
}
