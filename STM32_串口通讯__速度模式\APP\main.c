#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0步进电机驱动库 - 双电机控制示例
***	原作者：ZHANGDATOU
***	技术支持：张大头机器人
***	淘宝店铺：https://zhangdatou.taobao.com
***	CSDN博客：https://blog.csdn.net/zhangdatou666
***	qq技术群：262438510
***	双电机扩展：同时控制两个步进电机
**********************************************************/

/**********************************************************
***	回零命令函数定义
**********************************************************/

/**
 * @brief 电机1回零命令
 */
void motor1_return_to_zero(void)
{
	Emm_V5_En_Control(1, true, false);  // 确保电机使能
	delay_ms(100);
	// 使用绝对位置控制回到零点
	Emm_V5_Pos_Control(1, 0, 800, 10, 0, true, false);
	delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 电机2回零命令（修复版本）
 */
void motor2_return_to_zero(void)
{
	// 尝试不同地址的使能
	Emm_V5_En_Control_USART(2, 1, true, false);  // 地址1
	delay_ms(100);
	Emm_V5_En_Control_USART(2, 2, true, false);  // 地址2
	delay_ms(100);

	// 尝试不同地址的回零
	Emm_V5_Pos_Control_USART(2, 1, 0, 800, 10, 0, true, false);  // 地址1
	delay_ms(100);
	Emm_V5_Pos_Control_USART(2, 2, 0, 800, 10, 0, true, false);  // 地址2
	delay_ms(2000);  // 等待回零完成
}

/**
 * @brief 双电机同步回零命令
 */
void dual_motor_return_to_zero(void)
{
	Emm_V5_En_Control(1, true, false);
	Emm_V5_En_Control_USART(2, 1, true, false);
	delay_ms(100);

	// 同步回零：设置同步标志为true
	Emm_V5_Pos_Control(1, 0, 800, 10, 0, true, true);
	Emm_V5_Pos_Control_USART(2, 1, 0, 800, 10, 0, true, true);
	delay_ms(100);

	// 触发同步回零
	Emm_V5_Synchronous_motion(1);
	delay_ms(2000);  // 等待回零完成
}

/**
	*	@brief		主函数 - 双电机位置控制演示（带零点设置和回零功能）
	*	@param		无
	*	@retval		无
	*/
int main(void)
{
/**********************************************************
***	初始化系统硬件
**********************************************************/
	board_init();

/**********************************************************
***	上电延时2秒等待Emm_V5.0驱动器初始化完成
**********************************************************/
	delay_ms(2000);

/**********************************************************
***	使能两个电机并设置零点位置（关键步骤！）
**********************************************************/
	// 使能电机1 (USART1)
	Emm_V5_En_Control(1, true, false);
	delay_ms(100);

	// 使能电机2 (USART2) - 多次尝试不同地址
	Emm_V5_En_Control_USART(2, 1, true, false);  // 尝试地址1
	delay_ms(100);
	Emm_V5_En_Control_USART(2, 2, true, false);  // 尝试地址2
	delay_ms(100);

/**********************************************************
***	设置当前位置为零点位置（修复电机2问题）
**********************************************************/
	// 电机1复位（正常）
	Emm_V5_Reset_CurPos_To_Zero(1);
	delay_ms(200);

	// 电机2复位 - 多种方法尝试
	// 方法1：地址1复位
	Emm_V5_Reset_CurPos_To_Zero_USART(2, 1);
	delay_ms(200);

	// 方法2：地址2复位（如果驱动器地址设置为2）
	Emm_V5_Reset_CurPos_To_Zero_USART(2, 2);
	delay_ms(200);

	// 方法3：原点设置（地址1）
	Emm_V5_Origin_Set_O_USART(2, 1, false);
	delay_ms(200);

	// 方法4：原点设置（地址2）
	Emm_V5_Origin_Set_O_USART(2, 2, false);
	delay_ms(200);

/**********************************************************
***	双电机位置模式控制 - 180°每秒旋转
***	电机1(USART1): 顺时针180°，速度1600RPM，1600脉冲
***	电机2(USART2): 逆时针180°，速度1600RPM，1600脉冲
***	计算公式：180° = 1600脉冲，180°/秒 = 1600RPM
**********************************************************/
  // 电机1 - 顺时针180°每秒
  Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);
	

  // 电机2 - 逆时针180°每秒（尝试不同地址）
  Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false);  // 地址1
  delay_ms(100);

	
	
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	WHILEѭ�� - ˫������ʾ
**********************************************************/
	while(1)
	{
		// 等待第一次运动完成后，间隔2秒进行下一次运动
		delay_ms(2000);

//		// 第二次位置运动：继续相同方向180°
//		Emm_V5_Pos_Control(1, 0, 1600, 15, 1600, false, false);        // 电机1顺时针180°
//		Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 1600, false, false); // 电机2逆时针180°
//		delay_ms(1200);  // 等待运动完成

//		delay_ms(2000);  // 间隔2秒

		// 第三次位置运动：切换方向180°
		Emm_V5_Pos_Control(1, 1, 1600, 15, 1600, false, false);        // 电机1逆时针180°
		Emm_V5_Pos_Control_USART(2, 1, 0, 1600, 15, 1600, false, false); // 电机2顺时针180°
		delay_ms(1200);  // 等待运动完成

//		delay_ms(2000);  // 间隔2秒

//		// 第四次位置运动：回到初始方向180°
//		Emm_V5_Pos_Control(1, 0, 1600, 15, 400, false, false);        // 电机1顺时针180°
//		Emm_V5_Pos_Control_USART(2, 1, 1, 1600, 15, 3200, false, false); // 电机2逆时针180°
//		delay_ms(1200);  // 等待运动完成

//		delay_ms(3000);  // 间隔3秒

//		// 演示回零功能：双电机同步回到零点位置
		dual_motor_return_to_zero();
		delay_ms(2000);  // 在零点停留2秒
	}
}
