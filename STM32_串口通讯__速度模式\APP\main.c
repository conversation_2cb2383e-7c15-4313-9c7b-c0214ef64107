#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0�����ջ���������
***	��д���ߣ�ZHANGDATOU
***	����֧�֣��Ŵ�ͷ�ջ��ŷ�
***	�Ա����̣�https://zhangdatou.taobao.com
***	CSDN���ͣ�http s://blog.csdn.net/zhangdatou666
***	qq����Ⱥ��262438510
**********************************************************/

/**
	*	@brief		MAIN����
	*	@param		��
	*	@retval		��
	*/
int main(void)
{
/**********************************************************
***	��ʼ����������
**********************************************************/
	board_init();

/**********************************************************
***	�ϵ���ʱ2��ȴ�Emm_V5.0�ջ���ʼ�����
**********************************************************/	
	delay_ms(2000);

/**********************************************************
***	�ٶ�ģʽ������CW���ٶ�1000RPM�����ٶ�10
**********************************************************/	
  // ����1 - ʹ��USART1
  Emm_V5_Vel_Control(1, 0, 1000, 10, 0);

  // ����2 - ʹ��USART2
  Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, 0);
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	WHILEѭ�� - ˫������ʾ
**********************************************************/
	while(1)
	{
		// ÿ5���л�һ�ε��ת����
		delay_ms(5000);

		// ֹͣ������
		Emm_V5_Stop_Now(1, false);           // ֹͣ����1
		Emm_V5_Stop_Now_USART(2, 1, false);  // ֹͣ����2
		delay_ms(1000);

		// �л�����
		Emm_V5_Vel_Control(1, 1, 800, 10, 0);        // ����1��ʱ��800RPM
		Emm_V5_Vel_Control_USART(2, 1, 0, 1200, 15, 0); // ����2˳ʱ��1200RPM
		delay_ms(5000);

		// ֹͣ������
		Emm_V5_Stop_Now(1, false);
		Emm_V5_Stop_Now_USART(2, 1, false);
		delay_ms(1000);

		// �ָ���ʼ״̬
		Emm_V5_Vel_Control(1, 0, 1000, 10, 0);       // ����1˳ʱ��1000RPM
		Emm_V5_Vel_Control_USART(2, 1, 1, 800, 15, 0);  // ����2��ʱ��800RPM
	}
}
